package com.pokecobble.ui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.ClientSyncManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * Manages automatic data refresh for UI components that display server data.
 * Ensures UI components update when underlying data changes.
 */
@Environment(EnvType.CLIENT)
public class UIDataRefreshManager {
    private static UIDataRefreshManager instance;
    
    // Registered UI components and their refresh handlers
    private final Map<String, Set<RefreshableComponent>> registeredComponents = new ConcurrentHashMap<>();
    
    // Screen-specific refresh handlers
    private final Map<Class<? extends Screen>, Set<Runnable>> screenRefreshHandlers = new ConcurrentHashMap<>();
    
    // Active screens tracking
    private final Set<Screen> activeScreens = new HashSet<>();
    
    // Data change tracking
    private final Map<String, Long> lastDataChangeTime = new ConcurrentHashMap<>();
    private final Map<String, Integer> dataVersions = new ConcurrentHashMap<>();
    
    // Configuration
    private static final long REFRESH_THROTTLE_MS = 100; // 100ms throttle for refresh calls
    
    private UIDataRefreshManager() {
        setupDataChangeHandlers();
    }
    
    public static UIDataRefreshManager getInstance() {
        if (instance == null) {
            instance = new UIDataRefreshManager();
        }
        return instance;
    }
    
    /**
     * Sets up handlers for data change events.
     */
    private void setupDataChangeHandlers() {
        ClientSyncManager syncManager = ClientSyncManager.getInstance();
        
        // Town data changes
        syncManager.addEventHandler("town_data_updated", (data) -> {
            refreshComponents("town_data");
            refreshScreensOfType(com.pokecobble.town.gui.MyTownScreen.class);
            refreshScreensOfType(com.pokecobble.town.gui.ModernTownScreen.class);
            // Browser screen refresh removed
        });
        
        // Player data changes
        syncManager.addEventHandler("player_data_updated", (data) -> {
            refreshComponents("player_data");
            refreshScreensOfType(com.pokecobble.town.gui.MyTownScreen.class);
        });
        
        // Chunk data changes
        syncManager.addEventHandler("chunk_data_updated", (data) -> {
            refreshComponents("chunk_data");
            refreshClaimToolComponents();
        });
        
        // Election changes
        syncManager.addEventHandler("election_updated", (data) -> {
            refreshComponents("election_data");
            refreshScreensOfType(com.pokecobble.town.gui.MyTownScreen.class);
        });
        
        // Money changes
        syncManager.addEventHandler("money_updated", (data) -> {
            refreshComponents("money_data");
            refreshPhoneApps("bank");
        });
        
        // Permission changes
        syncManager.addEventHandler("permission_updated", (data) -> {
            refreshComponents("permission_data");
            refreshScreensOfType(com.pokecobble.town.gui.MyTownScreen.class);
        });
        
        // Notification changes
        syncManager.addEventHandler("notifications_updated", (data) -> {
            refreshComponents("notification_data");
            refreshPhoneApps("notifications");
        });
        
        // Invitation changes
        syncManager.addEventHandler("invitations_updated", (data) -> {
            refreshComponents("invitation_data");
            refreshPhoneApps("invitations");
        });
        
        Pokecobbleclaim.LOGGER.debug("Set up UI data refresh handlers");
    }
    
    /**
     * Registers a refreshable component for a specific data type.
     */
    public void registerComponent(String dataType, RefreshableComponent component) {
        registeredComponents.computeIfAbsent(dataType, k -> new HashSet<>()).add(component);
        Pokecobbleclaim.LOGGER.debug("Registered UI component for data type: " + dataType);
    }
    
    /**
     * Unregisters a refreshable component.
     */
    public void unregisterComponent(String dataType, RefreshableComponent component) {
        Set<RefreshableComponent> components = registeredComponents.get(dataType);
        if (components != null) {
            components.remove(component);
            if (components.isEmpty()) {
                registeredComponents.remove(dataType);
            }
        }
    }
    
    /**
     * Registers a screen-specific refresh handler.
     */
    public void registerScreenRefreshHandler(Class<? extends Screen> screenClass, Runnable refreshHandler) {
        screenRefreshHandlers.computeIfAbsent(screenClass, k -> new HashSet<>()).add(refreshHandler);
        Pokecobbleclaim.LOGGER.debug("Registered screen refresh handler for: " + screenClass.getSimpleName());
    }
    
    /**
     * Unregisters a screen-specific refresh handler.
     */
    public void unregisterScreenRefreshHandler(Class<? extends Screen> screenClass, Runnable refreshHandler) {
        Set<Runnable> handlers = screenRefreshHandlers.get(screenClass);
        if (handlers != null) {
            handlers.remove(refreshHandler);
            if (handlers.isEmpty()) {
                screenRefreshHandlers.remove(screenClass);
            }
        }
    }
    
    /**
     * Tracks when a screen becomes active.
     */
    public void onScreenOpened(Screen screen) {
        activeScreens.add(screen);
        
        // Trigger initial data refresh for the screen
        refreshScreen(screen);
        
        Pokecobbleclaim.LOGGER.debug("Screen opened: " + screen.getClass().getSimpleName());
    }
    
    /**
     * Tracks when a screen is closed.
     */
    public void onScreenClosed(Screen screen) {
        activeScreens.remove(screen);
        Pokecobbleclaim.LOGGER.debug("Screen closed: " + screen.getClass().getSimpleName());
    }
    
    /**
     * Refreshes components for a specific data type.
     */
    private void refreshComponents(String dataType) {
        // Check throttling
        Long lastChange = lastDataChangeTime.get(dataType);
        long currentTime = System.currentTimeMillis();
        if (lastChange != null && currentTime - lastChange < REFRESH_THROTTLE_MS) {
            return; // Throttled
        }
        
        lastDataChangeTime.put(dataType, currentTime);
        
        // Update version
        dataVersions.put(dataType, dataVersions.getOrDefault(dataType, 0) + 1);
        
        // Refresh registered components
        Set<RefreshableComponent> components = registeredComponents.get(dataType);
        if (components != null) {
            for (RefreshableComponent component : components) {
                try {
                    component.refresh();
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error refreshing UI component: " + e.getMessage());
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Refreshed " + components.size() + " components for data type: " + dataType);
        }
    }
    
    /**
     * Refreshes all screens of a specific type.
     */
    private void refreshScreensOfType(Class<? extends Screen> screenClass) {
        // Refresh active screens of this type
        for (Screen screen : activeScreens) {
            if (screenClass.isInstance(screen)) {
                refreshScreen(screen);
            }
        }
        
        // Call registered refresh handlers
        Set<Runnable> handlers = screenRefreshHandlers.get(screenClass);
        if (handlers != null) {
            for (Runnable handler : handlers) {
                try {
                    handler.run();
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in screen refresh handler: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Refreshes a specific screen.
     */
    private void refreshScreen(Screen screen) {
        try {
            // Call refresh method if the screen implements RefreshableComponent
            if (screen instanceof RefreshableComponent) {
                ((RefreshableComponent) screen).refresh();
            }
            // Call specific refresh methods for known screen types
            else if (screen instanceof com.pokecobble.town.gui.MyTownScreen) {
                ((com.pokecobble.town.gui.MyTownScreen) screen).refreshPlayerList();
            }
            else if (screen instanceof com.pokecobble.town.gui.ModernTownScreen) {
                // ModernTownScreen has its own refresh mechanism
                screen.init(MinecraftClient.getInstance(), screen.width, screen.height);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing screen " + screen.getClass().getSimpleName() + ": " + e.getMessage());
        }
    }
    
    // Browser screen refresh method removed
    
    /**
     * Refreshes phone apps with specific data.
     */
    private void refreshPhoneApps(String appType) {
        try {
            // Trigger phone app refresh through the client sync manager
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("phone_" + appType + "_updated", null);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing phone apps: " + e.getMessage());
        }
    }
    
    /**
     * Refreshes claim tool components.
     */
    private void refreshClaimToolComponents() {
        try {
            // Force refresh of claim tool data
            com.pokecobble.town.client.ClaimToolDataSynchronizer.getInstance().forceUpdate();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing claim tool components: " + e.getMessage());
        }
    }
    
    /**
     * Forces a refresh of all registered components.
     */
    public void forceRefreshAll() {
        for (String dataType : registeredComponents.keySet()) {
            refreshComponents(dataType);
        }
        
        // Refresh all active screens
        for (Screen screen : activeScreens) {
            refreshScreen(screen);
        }
        
        Pokecobbleclaim.LOGGER.debug("Forced refresh of all UI components");
    }
    
    /**
     * Gets the current data version for a specific data type.
     */
    public int getDataVersion(String dataType) {
        return dataVersions.getOrDefault(dataType, 0);
    }
    
    /**
     * Gets statistics about registered components.
     */
    public Map<String, Object> getRefreshStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("registeredDataTypes", registeredComponents.size());
        stats.put("activeScreens", activeScreens.size());
        stats.put("screenRefreshHandlers", screenRefreshHandlers.size());
        
        // Count total components
        int totalComponents = registeredComponents.values().stream()
                .mapToInt(Set::size)
                .sum();
        stats.put("totalComponents", totalComponents);
        
        return stats;
    }
    
    /**
     * Interface for components that can be refreshed.
     */
    public interface RefreshableComponent {
        /**
         * Refreshes the component's data.
         */
        void refresh();
        
        /**
         * Gets the data types this component depends on.
         */
        default Set<String> getDependentDataTypes() {
            return Set.of();
        }
    }
    
    /**
     * Utility method to create a simple refreshable component.
     */
    public static RefreshableComponent createSimpleComponent(Runnable refreshAction, String... dataTypes) {
        return new RefreshableComponent() {
            @Override
            public void refresh() {
                refreshAction.run();
            }
            
            @Override
            public Set<String> getDependentDataTypes() {
                return Set.of(dataTypes);
            }
        };
    }
}
