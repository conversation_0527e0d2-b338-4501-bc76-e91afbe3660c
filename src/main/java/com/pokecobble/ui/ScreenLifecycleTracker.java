package com.pokecobble.ui;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Tracks screen lifecycle events and automatically manages UI data refresh.
 */
@Environment(EnvType.CLIENT)
public class ScreenLifecycleTracker {
    private static ScreenLifecycleTracker instance;
    
    // Screen tracking
    private Screen currentScreen = null;
    private final Map<Screen, Long> screenOpenTimes = new ConcurrentHashMap<>();
    private final Map<Class<? extends Screen>, Integer> screenOpenCounts = new ConcurrentHashMap<>();
    
    // Lifecycle listeners
    private final Set<ScreenLifecycleListener> listeners = new HashSet<>();
    
    // Auto-refresh configuration
    private final Map<Class<? extends Screen>, Long> autoRefreshIntervals = new ConcurrentHashMap<>();
    private final Map<Screen, Long> lastAutoRefresh = new ConcurrentHashMap<>();
    
    // Default auto-refresh intervals (in milliseconds)
    private static final long DEFAULT_TOWN_SCREEN_REFRESH = 5000; // 5 seconds
    // Browser screen refresh removed
    private static final long DEFAULT_PHONE_SCREEN_REFRESH = 2000; // 2 seconds
    
    private ScreenLifecycleTracker() {
        setupDefaultAutoRefreshIntervals();
        startPeriodicRefreshCheck();
    }
    
    public static ScreenLifecycleTracker getInstance() {
        if (instance == null) {
            instance = new ScreenLifecycleTracker();
        }
        return instance;
    }
    
    /**
     * Sets up default auto-refresh intervals for known screen types.
     */
    private void setupDefaultAutoRefreshIntervals() {
        // Town management screens
        autoRefreshIntervals.put(com.pokecobble.town.gui.MyTownScreen.class, DEFAULT_TOWN_SCREEN_REFRESH);
        autoRefreshIntervals.put(com.pokecobble.town.gui.ModernTownScreen.class, DEFAULT_TOWN_SCREEN_REFRESH);
        
        // Browser screens removed
        
        // Phone screens
        autoRefreshIntervals.put(com.pokecobble.phone.gui.PhoneScreen.class, DEFAULT_PHONE_SCREEN_REFRESH);
        
        Pokecobbleclaim.LOGGER.debug("Set up default auto-refresh intervals for screen types");
    }
    
    /**
     * Starts the periodic refresh check.
     */
    private void startPeriodicRefreshCheck() {
        // This would ideally use a proper scheduler, but for now we'll check during screen updates
        Pokecobbleclaim.LOGGER.debug("Started periodic refresh check");
    }
    
    /**
     * Tracks screen changes and notifies listeners.
     */
    public void updateCurrentScreen() {
        MinecraftClient client = MinecraftClient.getInstance();
        Screen newScreen = client.currentScreen;
        
        // Check if screen changed
        if (newScreen != currentScreen) {
            Screen oldScreen = currentScreen;
            currentScreen = newScreen;
            
            // Handle screen close
            if (oldScreen != null) {
                handleScreenClosed(oldScreen);
            }
            
            // Handle screen open
            if (newScreen != null) {
                handleScreenOpened(newScreen);
            }
        }
        
        // Check for auto-refresh
        if (currentScreen != null) {
            checkAutoRefresh(currentScreen);
        }
    }
    
    /**
     * Handles when a screen is opened.
     */
    private void handleScreenOpened(Screen screen) {
        try {
            // Track screen open time
            screenOpenTimes.put(screen, System.currentTimeMillis());
            
            // Update open count
            Class<? extends Screen> screenClass = screen.getClass();
            screenOpenCounts.put(screenClass, screenOpenCounts.getOrDefault(screenClass, 0) + 1);
            
            // Initialize auto-refresh tracking
            lastAutoRefresh.put(screen, System.currentTimeMillis());
            
            // Notify UI data refresh manager
            UIDataRefreshManager.getInstance().onScreenOpened(screen);
            
            // Notify listeners
            for (ScreenLifecycleListener listener : listeners) {
                try {
                    listener.onScreenOpened(screen);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in screen lifecycle listener: " + e.getMessage());
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Screen opened: " + screen.getClass().getSimpleName());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling screen open: " + e.getMessage());
        }
    }
    
    /**
     * Handles when a screen is closed.
     */
    private void handleScreenClosed(Screen screen) {
        try {
            // Calculate screen open duration
            Long openTime = screenOpenTimes.remove(screen);
            if (openTime != null) {
                long duration = System.currentTimeMillis() - openTime;
                Pokecobbleclaim.LOGGER.debug("Screen " + screen.getClass().getSimpleName() + " was open for " + duration + "ms");
            }
            
            // Clean up auto-refresh tracking
            lastAutoRefresh.remove(screen);
            
            // Notify UI data refresh manager
            UIDataRefreshManager.getInstance().onScreenClosed(screen);
            
            // Notify listeners
            for (ScreenLifecycleListener listener : listeners) {
                try {
                    listener.onScreenClosed(screen);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in screen lifecycle listener: " + e.getMessage());
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Screen closed: " + screen.getClass().getSimpleName());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling screen close: " + e.getMessage());
        }
    }
    
    /**
     * Checks if a screen needs auto-refresh.
     */
    private void checkAutoRefresh(Screen screen) {
        try {
            Class<? extends Screen> screenClass = screen.getClass();
            Long refreshInterval = autoRefreshIntervals.get(screenClass);
            
            if (refreshInterval != null) {
                Long lastRefresh = lastAutoRefresh.get(screen);
                long currentTime = System.currentTimeMillis();
                
                if (lastRefresh == null || currentTime - lastRefresh >= refreshInterval) {
                    // Trigger auto-refresh
                    performAutoRefresh(screen);
                    lastAutoRefresh.put(screen, currentTime);
                }
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error checking auto-refresh: " + e.getMessage());
        }
    }
    
    /**
     * Performs auto-refresh for a screen.
     */
    private void performAutoRefresh(Screen screen) {
        try {
            // Refresh based on screen type
            if (screen instanceof com.pokecobble.town.gui.MyTownScreen) {
                ((com.pokecobble.town.gui.MyTownScreen) screen).refreshPlayerList();
            }
            // Browser screen refresh removed
            else if (screen instanceof UIDataRefreshManager.RefreshableComponent) {
                ((UIDataRefreshManager.RefreshableComponent) screen).refresh();
            }
            
            Pokecobbleclaim.LOGGER.debug("Performed auto-refresh for screen: " + screen.getClass().getSimpleName());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error performing auto-refresh: " + e.getMessage());
        }
    }
    
    /**
     * Adds a screen lifecycle listener.
     */
    public void addListener(ScreenLifecycleListener listener) {
        listeners.add(listener);
    }
    
    /**
     * Removes a screen lifecycle listener.
     */
    public void removeListener(ScreenLifecycleListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * Sets the auto-refresh interval for a screen type.
     */
    public void setAutoRefreshInterval(Class<? extends Screen> screenClass, long intervalMs) {
        autoRefreshIntervals.put(screenClass, intervalMs);
        Pokecobbleclaim.LOGGER.debug("Set auto-refresh interval for " + screenClass.getSimpleName() + ": " + intervalMs + "ms");
    }
    
    /**
     * Disables auto-refresh for a screen type.
     */
    public void disableAutoRefresh(Class<? extends Screen> screenClass) {
        autoRefreshIntervals.remove(screenClass);
        Pokecobbleclaim.LOGGER.debug("Disabled auto-refresh for " + screenClass.getSimpleName());
    }
    
    /**
     * Gets the current screen.
     */
    public Screen getCurrentScreen() {
        return currentScreen;
    }
    
    /**
     * Checks if a specific screen type is currently open.
     */
    public boolean isScreenTypeOpen(Class<? extends Screen> screenClass) {
        return currentScreen != null && screenClass.isInstance(currentScreen);
    }
    
    /**
     * Gets statistics about screen usage.
     */
    public Map<String, Object> getScreenStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("currentScreen", currentScreen != null ? currentScreen.getClass().getSimpleName() : "none");
        stats.put("trackedScreens", screenOpenTimes.size());
        stats.put("autoRefreshScreenTypes", autoRefreshIntervals.size());
        stats.put("lifecycleListeners", listeners.size());
        
        // Screen open counts
        Map<String, Integer> openCounts = new HashMap<>();
        for (Map.Entry<Class<? extends Screen>, Integer> entry : screenOpenCounts.entrySet()) {
            openCounts.put(entry.getKey().getSimpleName(), entry.getValue());
        }
        stats.put("screenOpenCounts", openCounts);
        
        return stats;
    }
    
    /**
     * Forces refresh of the current screen.
     */
    public void forceRefreshCurrentScreen() {
        if (currentScreen != null) {
            performAutoRefresh(currentScreen);
            lastAutoRefresh.put(currentScreen, System.currentTimeMillis());
        }
    }
    
    /**
     * Interface for screen lifecycle listeners.
     */
    public interface ScreenLifecycleListener {
        /**
         * Called when a screen is opened.
         */
        default void onScreenOpened(Screen screen) {}
        
        /**
         * Called when a screen is closed.
         */
        default void onScreenClosed(Screen screen) {}
    }
}
