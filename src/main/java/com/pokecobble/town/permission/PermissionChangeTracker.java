package com.pokecobble.town.permission;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.math.ChunkPos;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Tracks permission changes and automatically triggers synchronization.
 * This class acts as a wrapper around permission modifications to ensure
 * all changes are properly synchronized to clients.
 */
public class PermissionChangeTracker {
    private static PermissionChangeTracker instance;
    
    // Change tracking
    private final Map<UUID, Set<String>> pendingPlayerChanges = new ConcurrentHashMap<>();
    private final Map<ChunkPos, Long> pendingChunkChanges = new ConcurrentHashMap<>();
    private final Map<UUID, TownPlayerRank> pendingRankChanges = new ConcurrentHashMap<>();
    
    // Batch processing
    private static final long BATCH_DELAY_MS = 100; // 100ms delay for batching changes
    private final Map<UUID, Long> lastPlayerChangeTime = new ConcurrentHashMap<>();
    private final Map<ChunkPos, Long> lastChunkChangeTime = new ConcurrentHashMap<>();
    
    private PermissionChangeTracker() {}
    
    public static PermissionChangeTracker getInstance() {
        if (instance == null) {
            instance = new PermissionChangeTracker();
        }
        return instance;
    }
    
    /**
     * Tracks a player permission change and triggers synchronization.
     */
    public void trackPlayerPermissionChange(MinecraftServer server, UUID playerId, String permissionCategory, String permissionName, boolean value) {
        try {
            // Validate inputs
            if (server == null || playerId == null || permissionCategory == null || permissionName == null) {
                return;
            }
            
            // Track the change
            String changeKey = permissionCategory + "." + permissionName;
            pendingPlayerChanges.computeIfAbsent(playerId, k -> new HashSet<>()).add(changeKey);
            lastPlayerChangeTime.put(playerId, System.currentTimeMillis());
            
            // Trigger immediate sync for critical permissions
            if (isCriticalPermission(permissionCategory, permissionName)) {
                PermissionSynchronizer.syncPlayerPermissionChange(server, playerId, permissionCategory, permissionName, value);
                
                // Remove from pending since we synced immediately
                Set<String> pending = pendingPlayerChanges.get(playerId);
                if (pending != null) {
                    pending.remove(changeKey);
                    if (pending.isEmpty()) {
                        pendingPlayerChanges.remove(playerId);
                    }
                }
            } else {
                // Schedule batched sync
                scheduleBatchedPlayerSync(server, playerId);
            }
            
            Pokecobbleclaim.LOGGER.debug("Tracked permission change for player " + playerId + ": " + changeKey + " = " + value);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error tracking player permission change: " + e.getMessage());
        }
    }
    
    /**
     * Tracks a chunk permission change and triggers synchronization.
     */
    public void trackChunkPermissionChange(MinecraftServer server, ChunkPos chunkPos, ClaimTag newTag) {
        try {
            // Validate inputs
            if (server == null || chunkPos == null) {
                return;
            }
            
            // Track the change
            pendingChunkChanges.put(chunkPos, System.currentTimeMillis());
            lastChunkChangeTime.put(chunkPos, System.currentTimeMillis());
            
            // Always sync chunk changes immediately (they affect gameplay directly)
            PermissionSynchronizer.syncChunkPermissionChange(server, chunkPos, newTag);
            
            // Remove from pending since we synced immediately
            pendingChunkChanges.remove(chunkPos);
            
            Pokecobbleclaim.LOGGER.debug("Tracked chunk permission change for chunk " + chunkPos);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error tracking chunk permission change: " + e.getMessage());
        }
    }
    
    /**
     * Tracks a player rank change and triggers synchronization.
     */
    public void trackPlayerRankChange(MinecraftServer server, UUID playerId, TownPlayerRank oldRank, TownPlayerRank newRank) {
        try {
            // Validate inputs
            if (server == null || playerId == null || newRank == null) {
                return;
            }
            
            // Track the change
            pendingRankChanges.put(playerId, newRank);
            
            // Always sync rank changes immediately (they affect all permissions)
            PermissionSynchronizer.syncPlayerRankChange(server, playerId, oldRank, newRank);
            
            // Remove from pending since we synced immediately
            pendingRankChanges.remove(playerId);
            
            // Clear any pending permission changes for this player since rank change affects all permissions
            pendingPlayerChanges.remove(playerId);
            lastPlayerChangeTime.remove(playerId);
            
            Pokecobbleclaim.LOGGER.debug("Tracked rank change for player " + playerId + ": " + oldRank + " -> " + newRank);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error tracking rank change: " + e.getMessage());
        }
    }
    
    /**
     * Schedules a batched sync for a player's permission changes.
     */
    private void scheduleBatchedPlayerSync(MinecraftServer server, UUID playerId) {
        // This would ideally use a scheduler, but for now we'll sync after a delay
        new Thread(() -> {
            try {
                Thread.sleep(BATCH_DELAY_MS);
                
                // Check if there are still pending changes
                Set<String> pending = pendingPlayerChanges.get(playerId);
                if (pending != null && !pending.isEmpty()) {
                    // Get the player's current permissions
                    Town town = TownManager.getInstance().getPlayerTown(playerId);
                    if (town != null) {
                        TownPlayer townPlayer = town.getPlayer(playerId);
                        if (townPlayer != null) {
                            // Sync all pending changes
                            for (String changeKey : pending) {
                                String[] parts = changeKey.split("\\.", 2);
                                if (parts.length == 2) {
                                    String category = parts[0];
                                    String permission = parts[1];
                                    boolean value = townPlayer.hasPermission(category, permission);
                                    
                                    PermissionSynchronizer.syncPlayerPermissionChange(server, playerId, category, permission, value);
                                }
                            }
                        }
                    }
                    
                    // Clear pending changes
                    pendingPlayerChanges.remove(playerId);
                    lastPlayerChangeTime.remove(playerId);
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error in batched permission sync: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * Checks if a permission is critical and should be synced immediately.
     */
    private boolean isCriticalPermission(String category, String permission) {
        // Critical permissions that affect gameplay immediately
        switch (category) {
            case "Claim Tool":
                return permission.equals("Can access claim tool") || permission.equals("Can delete claims");
            case "Player Management":
                return permission.equals("Can kick players") || permission.equals("Can change player ranks");
            case "Town Bank":
                return permission.equals("Can withdraw money");
            default:
                return false;
        }
    }
    
    /**
     * Forces immediate sync of all pending changes for a player.
     */
    public void forceSyncPlayer(MinecraftServer server, UUID playerId) {
        try {
            Set<String> pending = pendingPlayerChanges.remove(playerId);
            if (pending != null && !pending.isEmpty()) {
                // Get the player's current permissions
                Town town = TownManager.getInstance().getPlayerTown(playerId);
                if (town != null) {
                    TownPlayer townPlayer = town.getPlayer(playerId);
                    if (townPlayer != null) {
                        // Sync all pending changes
                        for (String changeKey : pending) {
                            String[] parts = changeKey.split("\\.", 2);
                            if (parts.length == 2) {
                                String category = parts[0];
                                String permission = parts[1];
                                boolean value = townPlayer.hasPermission(category, permission);
                                
                                PermissionSynchronizer.syncPlayerPermissionChange(server, playerId, category, permission, value);
                            }
                        }
                    }
                }
            }
            
            lastPlayerChangeTime.remove(playerId);
            
            Pokecobbleclaim.LOGGER.debug("Force synced all pending permission changes for player " + playerId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error force syncing player permissions: " + e.getMessage());
        }
    }
    
    /**
     * Forces immediate sync of all pending chunk changes.
     */
    public void forceSyncAllChunks(MinecraftServer server) {
        try {
            Map<ChunkPos, Long> pending = new HashMap<>(pendingChunkChanges);
            pendingChunkChanges.clear();
            
            for (ChunkPos chunkPos : pending.keySet()) {
                ClaimTag tag = com.pokecobble.town.claim.ChunkPermissionManager.getInstance().getTagForChunk(chunkPos);
                PermissionSynchronizer.syncChunkPermissionChange(server, chunkPos, tag);
            }
            
            lastChunkChangeTime.clear();
            
            Pokecobbleclaim.LOGGER.debug("Force synced " + pending.size() + " pending chunk permission changes");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error force syncing chunk permissions: " + e.getMessage());
        }
    }
    
    /**
     * Clears all pending changes for a player (called on disconnect).
     */
    public void clearPlayerChanges(UUID playerId) {
        pendingPlayerChanges.remove(playerId);
        pendingRankChanges.remove(playerId);
        lastPlayerChangeTime.remove(playerId);
        
        Pokecobbleclaim.LOGGER.debug("Cleared pending permission changes for player " + playerId);
    }
    
    /**
     * Gets statistics about pending changes.
     */
    public Map<String, Object> getChangeStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("pendingPlayerChanges", pendingPlayerChanges.size());
        stats.put("pendingChunkChanges", pendingChunkChanges.size());
        stats.put("pendingRankChanges", pendingRankChanges.size());
        
        // Count total pending permission changes
        int totalPermissionChanges = pendingPlayerChanges.values().stream()
                .mapToInt(Set::size)
                .sum();
        stats.put("totalPendingPermissionChanges", totalPermissionChanges);
        
        return stats;
    }
    
    /**
     * Performs periodic cleanup of old pending changes.
     */
    public void performCleanup() {
        long currentTime = System.currentTimeMillis();
        long maxAge = 30000; // 30 seconds
        
        // Clean up old player changes
        lastPlayerChangeTime.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() > maxAge) {
                pendingPlayerChanges.remove(entry.getKey());
                return true;
            }
            return false;
        });
        
        // Clean up old chunk changes
        lastChunkChangeTime.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() > maxAge) {
                pendingChunkChanges.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }
}
