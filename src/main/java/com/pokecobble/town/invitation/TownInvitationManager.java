package com.pokecobble.town.invitation;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages town invitations with proper tracking, expiry, and synchronization.
 */
public class TownInvitationManager {
    private static TownInvitationManager instance;
    
    // Active invitations: player UUID -> list of invitations
    private final Map<UUID, List<TownInvitation>> playerInvitations = new ConcurrentHashMap<>();
    
    // Invitation tracking: invitation ID -> invitation
    private final Map<UUID, TownInvitation> allInvitations = new ConcurrentHashMap<>();
    
    // Town invitation tracking: town UUID -> list of sent invitations
    private final Map<UUID, List<TownInvitation>> townInvitations = new ConcurrentHashMap<>();
    
    // Data versioning for synchronization
    private final Map<UUID, Integer> playerInvitationVersions = new ConcurrentHashMap<>();
    
    // Configuration
    private static final long DEFAULT_INVITATION_EXPIRY_MS = 300000; // 5 minutes
    private static final int MAX_INVITATIONS_PER_PLAYER = 10;
    private static final int MAX_INVITATIONS_PER_TOWN = 50;
    
    private TownInvitationManager() {}
    
    public static TownInvitationManager getInstance() {
        if (instance == null) {
            instance = new TownInvitationManager();
        }
        return instance;
    }
    
    /**
     * Sends a town invitation to a player.
     */
    public TownInvitation sendInvitation(MinecraftServer server, UUID fromTownId, UUID toPlayerId, UUID invitedByPlayerId) {
        // Validate parameters
        if (fromTownId == null || toPlayerId == null || invitedByPlayerId == null) {
            return null;
        }
        
        // Get town
        Town town = TownManager.getInstance().getTownById(fromTownId);
        if (town == null) {
            return null;
        }
        
        // Check if player is already in a town
        if (TownManager.getInstance().getPlayerTown(toPlayerId) != null) {
            return null;
        }
        
        // Check if player already has an invitation from this town
        if (hasInvitationFromTown(toPlayerId, fromTownId)) {
            return null;
        }
        
        // Check invitation limits
        if (getPlayerInvitationCount(toPlayerId) >= MAX_INVITATIONS_PER_PLAYER) {
            return null;
        }
        
        if (getTownInvitationCount(fromTownId) >= MAX_INVITATIONS_PER_TOWN) {
            return null;
        }
        
        // Create invitation
        TownInvitation invitation = new TownInvitation(
            UUID.randomUUID(),
            fromTownId,
            toPlayerId,
            invitedByPlayerId,
            System.currentTimeMillis(),
            System.currentTimeMillis() + DEFAULT_INVITATION_EXPIRY_MS
        );
        
        // Store invitation
        playerInvitations.computeIfAbsent(toPlayerId, k -> new ArrayList<>()).add(invitation);
        townInvitations.computeIfAbsent(fromTownId, k -> new ArrayList<>()).add(invitation);
        allInvitations.put(invitation.getId(), invitation);
        
        // Update version
        playerInvitationVersions.put(toPlayerId, playerInvitationVersions.getOrDefault(toPlayerId, 0) + 1);
        
        // Sync to client
        TownInvitationSynchronizer.syncPlayerInvitations(server, toPlayerId);
        
        // Send notification
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(toPlayerId);
        if (player != null) {
            // Add phone notification
            com.pokecobble.phone.network.PhoneDataSynchronizer.addNotificationForPlayer(
                toPlayerId,
                new com.pokecobble.phone.notification.PhoneNotification(
                    "Town Invite",
                    "You've been invited to join " + town.getName(),
                    com.pokecobble.phone.notification.PhoneNotification.Type.TOWN_INVITE,
                    6000 // 5 minutes
                )
            );
        }
        
        Pokecobbleclaim.LOGGER.info("Sent town invitation from " + town.getName() + " to player " + toPlayerId);
        return invitation;
    }
    
    /**
     * Accepts a town invitation.
     */
    public boolean acceptInvitation(MinecraftServer server, UUID invitationId, UUID playerId) {
        TownInvitation invitation = allInvitations.get(invitationId);
        if (invitation == null || !invitation.getToPlayerId().equals(playerId)) {
            return false;
        }
        
        // Check if invitation is still valid
        if (invitation.isExpired()) {
            removeInvitation(invitation);
            return false;
        }
        
        // Check if player is already in a town
        if (TownManager.getInstance().getPlayerTown(playerId) != null) {
            removeInvitation(invitation);
            return false;
        }
        
        // Get town
        Town town = TownManager.getInstance().getTownById(invitation.getFromTownId());
        if (town == null) {
            removeInvitation(invitation);
            return false;
        }
        
        // Check if town is full
        if (town.getPlayerCount() >= town.getMaxPlayers()) {
            removeInvitation(invitation);
            return false;
        }
        
        // Add player to town
        boolean added = TownManager.getInstance().addPlayerToTown(playerId, invitation.getFromTownId());
        if (!added) {
            return false;
        }
        
        // Remove invitation
        removeInvitation(invitation);
        
        // Sync to client
        TownInvitationSynchronizer.syncPlayerInvitations(server, playerId);
        
        Pokecobbleclaim.LOGGER.info("Player " + playerId + " accepted invitation to join " + town.getName());
        return true;
    }
    
    /**
     * Declines a town invitation.
     */
    public boolean declineInvitation(MinecraftServer server, UUID invitationId, UUID playerId) {
        TownInvitation invitation = allInvitations.get(invitationId);
        if (invitation == null || !invitation.getToPlayerId().equals(playerId)) {
            return false;
        }
        
        // Remove invitation
        removeInvitation(invitation);
        
        // Sync to client
        TownInvitationSynchronizer.syncPlayerInvitations(server, playerId);
        
        Pokecobbleclaim.LOGGER.info("Player " + playerId + " declined invitation to join town " + invitation.getFromTownId());
        return true;
    }
    
    /**
     * Removes an invitation.
     */
    private void removeInvitation(TownInvitation invitation) {
        // Remove from all tracking maps
        allInvitations.remove(invitation.getId());
        
        List<TownInvitation> playerInvites = playerInvitations.get(invitation.getToPlayerId());
        if (playerInvites != null) {
            playerInvites.remove(invitation);
            if (playerInvites.isEmpty()) {
                playerInvitations.remove(invitation.getToPlayerId());
            }
        }
        
        List<TownInvitation> townInvites = townInvitations.get(invitation.getFromTownId());
        if (townInvites != null) {
            townInvites.remove(invitation);
            if (townInvites.isEmpty()) {
                townInvitations.remove(invitation.getFromTownId());
            }
        }
        
        // Update version
        playerInvitationVersions.put(invitation.getToPlayerId(), 
                                   playerInvitationVersions.getOrDefault(invitation.getToPlayerId(), 0) + 1);
    }
    
    /**
     * Gets all invitations for a player.
     */
    public List<TownInvitation> getPlayerInvitations(UUID playerId) {
        List<TownInvitation> invitations = playerInvitations.get(playerId);
        return invitations != null ? new ArrayList<>(invitations) : new ArrayList<>();
    }
    
    /**
     * Gets all invitations sent by a town.
     */
    public List<TownInvitation> getTownInvitations(UUID townId) {
        List<TownInvitation> invitations = townInvitations.get(townId);
        return invitations != null ? new ArrayList<>(invitations) : new ArrayList<>();
    }
    
    /**
     * Checks if a player has an invitation from a specific town.
     */
    public boolean hasInvitationFromTown(UUID playerId, UUID townId) {
        List<TownInvitation> invitations = playerInvitations.get(playerId);
        if (invitations == null) {
            return false;
        }
        
        return invitations.stream().anyMatch(inv -> inv.getFromTownId().equals(townId) && !inv.isExpired());
    }
    
    /**
     * Gets the number of active invitations for a player.
     */
    public int getPlayerInvitationCount(UUID playerId) {
        List<TownInvitation> invitations = playerInvitations.get(playerId);
        if (invitations == null) {
            return 0;
        }
        
        return (int) invitations.stream().filter(inv -> !inv.isExpired()).count();
    }
    
    /**
     * Gets the number of active invitations sent by a town.
     */
    public int getTownInvitationCount(UUID townId) {
        List<TownInvitation> invitations = townInvitations.get(townId);
        if (invitations == null) {
            return 0;
        }
        
        return (int) invitations.stream().filter(inv -> !inv.isExpired()).count();
    }
    
    /**
     * Gets the invitation version for a player.
     */
    public int getPlayerInvitationVersion(UUID playerId) {
        return playerInvitationVersions.getOrDefault(playerId, 0);
    }
    
    /**
     * Cleans up expired invitations.
     */
    public void cleanupExpiredInvitations(MinecraftServer server) {
        List<TownInvitation> expiredInvitations = new ArrayList<>();
        
        // Find expired invitations
        for (TownInvitation invitation : allInvitations.values()) {
            if (invitation.isExpired()) {
                expiredInvitations.add(invitation);
            }
        }
        
        // Remove expired invitations
        Set<UUID> affectedPlayers = new HashSet<>();
        for (TownInvitation invitation : expiredInvitations) {
            affectedPlayers.add(invitation.getToPlayerId());
            removeInvitation(invitation);
        }
        
        // Sync to affected players
        for (UUID playerId : affectedPlayers) {
            TownInvitationSynchronizer.syncPlayerInvitations(server, playerId);
        }
        
        if (!expiredInvitations.isEmpty()) {
            Pokecobbleclaim.LOGGER.debug("Cleaned up " + expiredInvitations.size() + " expired invitations");
        }
    }
    
    /**
     * Clears all invitations for a player (called on disconnect).
     */
    public void clearPlayerInvitations(UUID playerId) {
        List<TownInvitation> invitations = playerInvitations.remove(playerId);
        if (invitations != null) {
            for (TownInvitation invitation : invitations) {
                allInvitations.remove(invitation.getId());
                
                // Remove from town tracking
                List<TownInvitation> townInvites = townInvitations.get(invitation.getFromTownId());
                if (townInvites != null) {
                    townInvites.remove(invitation);
                    if (townInvites.isEmpty()) {
                        townInvitations.remove(invitation.getFromTownId());
                    }
                }
            }
        }
        
        playerInvitationVersions.remove(playerId);
    }
    
    /**
     * Gets statistics about invitations.
     */
    public Map<String, Integer> getInvitationStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("totalInvitations", allInvitations.size());
        stats.put("playersWithInvitations", playerInvitations.size());
        stats.put("townsWithInvitations", townInvitations.size());
        
        // Count expired invitations
        int expiredCount = 0;
        for (TownInvitation invitation : allInvitations.values()) {
            if (invitation.isExpired()) {
                expiredCount++;
            }
        }
        stats.put("expiredInvitations", expiredCount);
        
        return stats;
    }
}
