package com.pokecobble.town.invitation;

import java.util.UUID;

/**
 * Represents a town invitation with tracking and expiry.
 */
public class TownInvitation {
    private final UUID id;
    private final UUID fromTownId;
    private final UUID toPlayerId;
    private final UUID invitedByPlayerId;
    private final long createdTime;
    private final long expiryTime;
    private InvitationStatus status;
    
    public enum InvitationStatus {
        PENDING,
        ACCEPTED,
        DECLINED,
        EXPIRED,
        CANCELLED
    }
    
    /**
     * Creates a new town invitation.
     */
    public TownInvitation(UUID id, UUID fromTownId, UUID toPlayerId, UUID invitedByPlayerId, 
                         long createdTime, long expiryTime) {
        this.id = id;
        this.fromTownId = fromTownId;
        this.toPlayerId = toPlayerId;
        this.invitedByPlayerId = invitedByPlayerId;
        this.createdTime = createdTime;
        this.expiryTime = expiryTime;
        this.status = InvitationStatus.PENDING;
    }
    
    /**
     * Gets the invitation ID.
     */
    public UUID getId() {
        return id;
    }
    
    /**
     * Gets the town ID that sent the invitation.
     */
    public UUID getFromTownId() {
        return fromTownId;
    }
    
    /**
     * Gets the player ID that received the invitation.
     */
    public UUID getToPlayerId() {
        return toPlayerId;
    }
    
    /**
     * Gets the player ID that sent the invitation.
     */
    public UUID getInvitedByPlayerId() {
        return invitedByPlayerId;
    }
    
    /**
     * Gets the creation time.
     */
    public long getCreatedTime() {
        return createdTime;
    }
    
    /**
     * Gets the expiry time.
     */
    public long getExpiryTime() {
        return expiryTime;
    }
    
    /**
     * Gets the invitation status.
     */
    public InvitationStatus getStatus() {
        return status;
    }
    
    /**
     * Sets the invitation status.
     */
    public void setStatus(InvitationStatus status) {
        this.status = status;
    }
    
    /**
     * Checks if the invitation is expired.
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > expiryTime || status == InvitationStatus.EXPIRED;
    }
    
    /**
     * Checks if the invitation is still pending.
     */
    public boolean isPending() {
        return status == InvitationStatus.PENDING && !isExpired();
    }
    
    /**
     * Gets the time remaining until expiry in milliseconds.
     */
    public long getTimeRemaining() {
        if (isExpired()) {
            return 0;
        }
        return Math.max(0, expiryTime - System.currentTimeMillis());
    }
    
    /**
     * Gets the time remaining until expiry in seconds.
     */
    public long getTimeRemainingSeconds() {
        return getTimeRemaining() / 1000;
    }
    
    /**
     * Gets the age of the invitation in milliseconds.
     */
    public long getAge() {
        return System.currentTimeMillis() - createdTime;
    }
    
    /**
     * Gets the age of the invitation in seconds.
     */
    public long getAgeSeconds() {
        return getAge() / 1000;
    }
    
    /**
     * Marks the invitation as expired.
     */
    public void expire() {
        this.status = InvitationStatus.EXPIRED;
    }
    
    /**
     * Marks the invitation as accepted.
     */
    public void accept() {
        this.status = InvitationStatus.ACCEPTED;
    }
    
    /**
     * Marks the invitation as declined.
     */
    public void decline() {
        this.status = InvitationStatus.DECLINED;
    }
    
    /**
     * Marks the invitation as cancelled.
     */
    public void cancel() {
        this.status = InvitationStatus.CANCELLED;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TownInvitation that = (TownInvitation) obj;
        return id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
    
    @Override
    public String toString() {
        return "TownInvitation{" +
                "id=" + id +
                ", fromTownId=" + fromTownId +
                ", toPlayerId=" + toPlayerId +
                ", invitedByPlayerId=" + invitedByPlayerId +
                ", status=" + status +
                ", timeRemaining=" + getTimeRemainingSeconds() + "s" +
                '}';
    }
}
