package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;
import net.minecraft.util.math.ChunkPos;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Represents a tag that can be applied to claimed chunks to control permissions.
 */
public class ClaimTag {
    private final UUID id;
    private String name;
    private String description;
    private int color;
    private Set<ChunkPos> chunks = new HashSet<>();

    // Permission settings using the new RankPermissions system
    private final RankPermissions permissions = new RankPermissions();

    // Keep these fields for backward compatibility
    // Non-member permissions - all set to false by default
    private boolean allowBuild = false;
    private boolean allowInteract = false; // Explicitly set to false
    private boolean allowContainers = false;
    private boolean allowRedstone = false;
    private boolean allowDoors = false;
    private boolean allowCrops = false;
    private boolean allowAnimals = false;
    private boolean allowVillagers = false;

    // Minimum rank permissions - only <PERSON><PERSON><PERSON><PERSON> (Mayor) has permissions by default
    private TownPlayerRank minRankBuild = TownPlayerRank.OWNER;
    private TownPlayerRank minRankInteract = TownPlayerRank.OWNER;
    private TownPlayerRank minRankContainers = TownPlayerRank.OWNER;
    private TownPlayerRank minRankRedstone = TownPlayerRank.OWNER;
    private TownPlayerRank minRankDoors = TownPlayerRank.OWNER;
    private TownPlayerRank minRankCrops = TownPlayerRank.OWNER;
    private TownPlayerRank minRankAnimals = TownPlayerRank.OWNER;
    private TownPlayerRank minRankVillagers = TownPlayerRank.OWNER;

    /**
     * Creates a new claim tag with the given name and color.
     */
    public ClaimTag(String name, int color) {
        this.id = UUID.randomUUID();
        this.name = name;
        this.description = "";
        this.color = color;
    }

    /**
     * Creates a new claim tag with the given name, description, and color.
     */
    public ClaimTag(String name, String description, int color) {
        this.id = UUID.randomUUID();
        this.name = name;
        this.description = description != null ? description : "";
        this.color = color;
    }

    /**
     * Gets the unique ID of this tag.
     */
    public UUID getId() {
        return id;
    }

    /**
     * Gets the name of this tag.
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the name of this tag.
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the description of this tag.
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the description of this tag.
     */
    public void setDescription(String description) {
        this.description = description != null ? description : "";
    }

    /**
     * Gets the color of this tag.
     */
    public int getColor() {
        return color;
    }

    /**
     * Sets the color of this tag.
     */
    public void setColor(int color) {
        this.color = color;
    }

    /**
     * Gets the chunks that have this tag.
     */
    public Set<ChunkPos> getChunks() {
        return new HashSet<>(chunks);
    }

    /**
     * Adds a chunk to this tag.
     */
    public void addChunk(ChunkPos chunk) {
        chunks.add(chunk);
    }

    /**
     * Removes a chunk from this tag.
     */
    public void removeChunk(ChunkPos chunk) {
        chunks.remove(chunk);
    }

    /**
     * Checks if this tag allows building for non-town members.
     */
    public boolean allowsBuild() {
        return permissions.hasPermission(null, 0);
    }

    /**
     * Sets whether this tag allows building for non-town members.
     */
    public void setAllowBuild(boolean allowBuild) {
        this.allowBuild = allowBuild;
        permissions.setPermission(null, 0, allowBuild);
    }

    /**
     * Checks if this tag allows interaction for non-town members.
     */
    public boolean allowsInteract() {
        return permissions.hasPermission(null, 1);
    }

    /**
     * Sets whether this tag allows interaction for non-town members.
     */
    public void setAllowInteract(boolean allowInteract) {
        this.allowInteract = allowInteract;
        permissions.setPermission(null, 1, allowInteract);
    }

    /**
     * Checks if this tag allows container access for non-town members.
     */
    public boolean allowsContainers() {
        return permissions.hasPermission(null, 2);
    }

    /**
     * Sets whether this tag allows container access for non-town members.
     */
    public void setAllowContainers(boolean allowContainers) {
        this.allowContainers = allowContainers;
        permissions.setPermission(null, 2, allowContainers);
    }

    /**
     * Gets the minimum rank required to build in chunks with this tag.
     */
    public TownPlayerRank getMinRankBuild() {
        return minRankBuild;
    }

    /**
     * Sets the minimum rank required to build in chunks with this tag.
     */
    public void setMinRankBuild(TownPlayerRank minRankBuild) {
        this.minRankBuild = minRankBuild;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankBuild != null && rank == minRankBuild) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 0, hasPermission);
        }
    }

    /**
     * Gets the minimum rank required to interact in chunks with this tag.
     */
    public TownPlayerRank getMinRankInteract() {
        return minRankInteract;
    }

    /**
     * Sets the minimum rank required to interact in chunks with this tag.
     */
    public void setMinRankInteract(TownPlayerRank minRankInteract) {
        this.minRankInteract = minRankInteract;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankInteract != null && rank == minRankInteract) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 1, hasPermission);
        }
    }

    /**
     * Gets the minimum rank required to access containers in chunks with this tag.
     */
    public TownPlayerRank getMinRankContainers() {
        return minRankContainers;
    }

    /**
     * Sets the minimum rank required to access containers in chunks with this tag.
     */
    public void setMinRankContainers(TownPlayerRank minRankContainers) {
        this.minRankContainers = minRankContainers;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankContainers != null && rank == minRankContainers) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 2, hasPermission);
        }
    }

    /**
     * Checks if this tag allows redstone for non-town members.
     */
    public boolean allowsRedstone() {
        return permissions.hasPermission(null, 3);
    }

    /**
     * Sets whether this tag allows redstone for non-town members.
     */
    public void setAllowRedstone(boolean allowRedstone) {
        this.allowRedstone = allowRedstone;
        permissions.setPermission(null, 3, allowRedstone);
    }

    /**
     * Gets the minimum rank required to use redstone in chunks with this tag.
     */
    public TownPlayerRank getMinRankRedstone() {
        return minRankRedstone;
    }

    /**
     * Sets the minimum rank required to use redstone in chunks with this tag.
     */
    public void setMinRankRedstone(TownPlayerRank minRankRedstone) {
        this.minRankRedstone = minRankRedstone;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankRedstone != null && rank == minRankRedstone) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 3, hasPermission);
        }
    }

    /**
     * Checks if this tag allows door usage for non-town members.
     */
    public boolean allowsDoors() {
        return permissions.hasPermission(null, 4);
    }

    /**
     * Sets whether this tag allows door usage for non-town members.
     */
    public void setAllowDoors(boolean allowDoors) {
        this.allowDoors = allowDoors;
        permissions.setPermission(null, 4, allowDoors);
    }

    /**
     * Gets the minimum rank required to use doors in chunks with this tag.
     */
    public TownPlayerRank getMinRankDoors() {
        return minRankDoors;
    }

    /**
     * Sets the minimum rank required to use doors in chunks with this tag.
     */
    public void setMinRankDoors(TownPlayerRank minRankDoors) {
        this.minRankDoors = minRankDoors;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankDoors != null && rank == minRankDoors) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 4, hasPermission);
        }
    }

    /**
     * Checks if this tag allows crop harvesting/planting for non-town members.
     */
    public boolean allowsCrops() {
        return permissions.hasPermission(null, 5);
    }

    /**
     * Sets whether this tag allows crop harvesting/planting for non-town members.
     */
    public void setAllowCrops(boolean allowCrops) {
        this.allowCrops = allowCrops;
        permissions.setPermission(null, 5, allowCrops);
    }

    /**
     * Gets the minimum rank required to harvest/plant crops in chunks with this tag.
     */
    public TownPlayerRank getMinRankCrops() {
        return minRankCrops;
    }

    /**
     * Sets the minimum rank required to harvest/plant crops in chunks with this tag.
     */
    public void setMinRankCrops(TownPlayerRank minRankCrops) {
        this.minRankCrops = minRankCrops;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankCrops != null && rank == minRankCrops) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 5, hasPermission);
        }
    }

    /**
     * Checks if this tag allows animal interaction for non-town members.
     */
    public boolean allowsAnimals() {
        return permissions.hasPermission(null, 6);
    }

    /**
     * Sets whether this tag allows animal interaction for non-town members.
     */
    public void setAllowAnimals(boolean allowAnimals) {
        this.allowAnimals = allowAnimals;
        permissions.setPermission(null, 6, allowAnimals);
    }

    /**
     * Gets the minimum rank required to interact with animals in chunks with this tag.
     */
    public TownPlayerRank getMinRankAnimals() {
        return minRankAnimals;
    }

    /**
     * Sets the minimum rank required to interact with animals in chunks with this tag.
     */
    public void setMinRankAnimals(TownPlayerRank minRankAnimals) {
        this.minRankAnimals = minRankAnimals;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankAnimals != null && rank == minRankAnimals) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 6, hasPermission);
        }
    }

    /**
     * Checks if this tag allows villager interaction for non-town members.
     */
    public boolean allowsVillagers() {
        return permissions.hasPermission(null, 7);
    }

    /**
     * Sets whether this tag allows villager interaction for non-town members.
     */
    public void setAllowVillagers(boolean allowVillagers) {
        this.allowVillagers = allowVillagers;
        permissions.setPermission(null, 7, allowVillagers);
    }

    /**
     * Gets the minimum rank required to interact with villagers in chunks with this tag.
     */
    public TownPlayerRank getMinRankVillagers() {
        return minRankVillagers;
    }

    /**
     * Sets the minimum rank required to interact with villagers in chunks with this tag.
     */
    public void setMinRankVillagers(TownPlayerRank minRankVillagers) {
        this.minRankVillagers = minRankVillagers;

        // Update permissions for all ranks
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean hasPermission = (minRankVillagers != null && rank == minRankVillagers) || rank == TownPlayerRank.OWNER;
            permissions.setPermission(rank, 7, hasPermission);
        }
    }

    /**
     * Gets the permissions for a specific rank.
     *
     * @param rank The rank to get permissions for, or null for non-member
     * @return An array of boolean values representing the permissions
     */
    public boolean[] getPermissionsForRank(TownPlayerRank rank) {
        return permissions.getPermissions(rank);
    }

    /**
     * Gets the rank permissions object.
     *
     * @return The rank permissions object
     */
    public RankPermissions getRankPermissions() {
        return permissions;
    }

    /**
     * Sets a specific permission for a specific rank.
     *
     * @param rank The rank to set the permission for, or null for non-member
     * @param permissionIndex The index of the permission to set
     * @param value Whether the rank should have the permission
     */
    public void setPermissionForRank(TownPlayerRank rank, int permissionIndex, boolean value) {
        permissions.setPermission(rank, permissionIndex, value);

        // Update the corresponding field for backward compatibility
        if (rank == null) {
            // Non-member permissions
            switch (permissionIndex) {
                case 0: allowBuild = value; break;
                case 1: allowInteract = value; break;
                case 2: allowContainers = value; break;
                case 3: allowRedstone = value; break;
                case 4: allowDoors = value; break;
                case 5: allowCrops = value; break;
                case 6: allowAnimals = value; break;
                case 7: allowVillagers = value; break;
            }
        } else {
            // Town member permissions
            // If this rank now has this permission, update the minRank field
            if (value) {
                switch (permissionIndex) {
                    case 0: minRankBuild = rank; break;
                    case 1: minRankInteract = rank; break;
                    case 2: minRankContainers = rank; break;
                    case 3: minRankRedstone = rank; break;
                    case 4: minRankDoors = rank; break;
                    case 5: minRankCrops = rank; break;
                    case 6: minRankAnimals = rank; break;
                    case 7: minRankVillagers = rank; break;
                }
            }
            // If this rank no longer has this permission, we don't need to update anything
            // The minRank field will still point to the rank that has the permission
        }
    }
}
