package com.pokecobble.town.claim;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.client.ChunkTracker;
import com.pokecobble.town.client.ClaimedChunkNotification;
import com.pokecobble.town.client.NotificationRenderer;
import com.pokecobble.town.network.NetworkManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.world.ClientWorld;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.math.ChunkPos;

import java.util.*;
import java.util.UUID;

/**
 * The claim tool is used to select and claim chunks.
 * It is a singleton class that is accessed through the getInstance() method.
 */
public class ClaimTool {
    // Singleton instance
    private static ClaimTool instance;

    // The Minecraft client instance
    private final MinecraftClient client = MinecraftClient.getInstance();

    // The current state of the claim tool
    private boolean active = false;
    private boolean showChunks = false;
    private int selectedTagIndex = 0;
    private final Set<ChunkPos> selectedChunks = new HashSet<>();

    // Listener for chunk selection changes
    private Runnable selectionChangeListener;
    private final Map<ChunkPos, ClaimTag> taggedChunks = new HashMap<>();
    private final Map<ChunkPos, String> claimedChunks = new HashMap<>();

    // The available claim tags
    private final List<ClaimTag> claimTags = new ArrayList<>();

    /**
     * Private constructor to prevent instantiation.
     * Use getInstance() instead.
     */
    private ClaimTool() {
        // Initialize the claim tags
        initClaimTags();
    }

    /**
     * Gets the singleton instance of the claim tool.
     *
     * @return The claim tool instance
     */
    public static ClaimTool getInstance() {
        if (instance == null) {
            instance = new ClaimTool();
        }
        return instance;
    }

    /**
     * Initializes the claim tags.
     * This is only used if no town is provided when activating the claim tool.
     */
    private void initClaimTags() {
        // Only add default tags if the list is empty
        if (claimTags.isEmpty()) {
            // Add default claim tags
            claimTags.add(new ClaimTag("tag1", 0xFF0000)); // Red
            claimTags.add(new ClaimTag("tag2", 0x00FF00)); // Green
            claimTags.add(new ClaimTag("tag3", 0x0000FF)); // Blue
            claimTags.add(new ClaimTag("tag4", 0xFFFF00)); // Yellow
            claimTags.add(new ClaimTag("tag5", 0xFF00FF)); // Magenta
            claimTags.add(new ClaimTag("tag6", 0x00FFFF)); // Cyan
            claimTags.add(new ClaimTag("tag7", 0xFFA500)); // Orange
            claimTags.add(new ClaimTag("tag8", 0x800080)); // Purple
        }
    }

    /**
     * Toggles the claim tool on or off.
     */
    public void toggle() {
        active = !active;
        if (active) {
            // Show chunks when activating the claim tool
            showChunks = true;
            // Clear selected chunks when activating the claim tool
            selectedChunks.clear();

            // The claimed chunks will be updated in the tick method

            // Try to load the player's town tags
            if (client.player != null) {
                Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
                if (playerTown != null) {
                    // Load the town's tags
                    List<ClaimTag> townTags = playerTown.getClaimTags();
                    if (townTags != null && !townTags.isEmpty()) {
                        // Clear existing tags and add the town's tags
                        claimTags.clear();
                        claimTags.addAll(townTags);
                        selectedTagIndex = 0; // Reset the selected tag index

                        // Notifications removed as requested
                        return;
                    }
                }
            }

            // Sound code removed
            // Notifications removed as requested
        } else {
            // Sound code removed
            // Notifications removed as requested
        }
    }



    /**
     * Toggles the visibility of chunk boundaries.
     */
    public void toggleChunkVisibility() {
        showChunks = !showChunks;
        if (showChunks) {
            // Sound code removed
            // Notifications removed as requested
        } else {
            // Sound code removed
            // Notifications removed as requested
        }
    }

    /**
     * Selects or deselects a chunk.
     *
     * @param chunkPos The chunk position
     */
    public void toggleChunkSelection(ChunkPos chunkPos) {
        boolean wasSelected = selectedChunks.contains(chunkPos);

        if (wasSelected) {
            // Deselect the chunk
            selectedChunks.remove(chunkPos);
            // Sound code removed
            // Notifications removed as requested
        } else {
            // Check if the chunk is already claimed by another town
            if (isChunkClaimed(chunkPos)) {
                // Sound code removed
                // Notifications removed as requested
                return;
            }

            // Check if the town has reached its claim limit
            Town playerTown = null;
            if (client.player != null) {
                playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
            }

            if (playerTown != null) {
                int usedClaims = playerTown.getClaimCount();
                int maxClaims = playerTown.getMaxClaims();
                int availableClaims = maxClaims - usedClaims;

                // Hard limit: If the town has already used all its claims, don't allow selecting any more
                if (usedClaims >= maxClaims) {
                    com.pokecobble.town.client.NotificationRenderer.addErrorNotification("Town has reached maximum claims: " + usedClaims + "/" + maxClaims);
                    return;
                }

                // Check if we have enough available claims (including already selected chunks)
                // This ensures we can't select more chunks than we have available claims
                if (selectedChunks.size() >= availableClaims) {
                    com.pokecobble.town.client.NotificationRenderer.addErrorNotification("Cannot select more chunks: " + usedClaims + "/" + maxClaims + " (" + selectedChunks.size() + " selected)");
                    return;
                }

                // Double-check that selecting this chunk won't exceed the maximum
                if (usedClaims + selectedChunks.size() >= maxClaims) {
                    com.pokecobble.town.client.NotificationRenderer.addErrorNotification("Town claim limit reached: " + usedClaims + "/" + maxClaims);
                    return;
                }
            }

            // Select the chunk
            selectedChunks.add(chunkPos);
            // Sound code removed
            // Notifications removed as requested
        }

        // Force a redraw of the HUD by scheduling a task for the next tick
        if (client != null) {
            client.execute(() -> {
                // This will trigger a redraw of the HUD
                // We don't need to do anything here, just scheduling the task is enough
            });
        }

        // Log the selection change
        Pokecobbleclaim.LOGGER.debug("Chunk selection changed: " + chunkPos + " selected: " + selectedChunks.contains(chunkPos));
    }

    /**
     * Tags a chunk with the current tag or deselects it if already selected.
     * No longer requires the chunk to be selected first.
     *
     * @param chunkPos The chunk position
     */
    public void tagChunk(ChunkPos chunkPos) {
        // Check if the chunk is claimed by another town
        if (isChunkClaimed(chunkPos)) {
            // Notifications removed as requested
            return;
        }

        // If the chunk is already selected, deselect it
        if (selectedChunks.contains(chunkPos)) {
            selectedChunks.remove(chunkPos);
            taggedChunks.remove(chunkPos);
            // Notifications removed as requested
            return;
        }

        // Check if the town has reached its claim limit
        if (hasReachedClaimLimit()) {
            // Town has reached its claim limit, don't allow selecting more chunks
            return;
        }

        // Check if selecting this chunk would exceed the town's claim limit
        Town playerTown = null;
        if (client.player != null) {
            playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
        }

        if (playerTown != null) {
            int usedClaims = playerTown.getClaimCount();
            int maxClaims = playerTown.getMaxClaims();
            int availableClaims = maxClaims - usedClaims;

            // Check if we have enough available claims (including already selected chunks)
            if (selectedChunks.size() >= availableClaims) {
                return;
            }
        }

        // Get the current tag
        ClaimTag tag = getCurrentTag();

        // Tag the chunk
        taggedChunks.put(chunkPos, tag);

        // Also select the chunk
        selectedChunks.add(chunkPos);

        // Sound code removed

        // Notifications removed as requested

        // The HUD will automatically update on the next render

        // Log the tag change
        Pokecobbleclaim.LOGGER.debug("Chunk tagged: " + chunkPos + " tag: " + tag.getName());
    }

    /**
     * Claims all selected chunks.
     * This is called when the player clicks the save button.
     */
    public void claimSelectedChunks() {
        saveSelectedChunks();
    }

    /**
     * Saves all selected chunks to the server.
     * This is called when exiting with the save option or when clicking the save button.
     */
    public void saveSelectedChunks() {
        if (selectedChunks.isEmpty()) {
            // Sound code removed
            // Notifications removed as requested
            return;
        }

        // Check if all selected chunks have tags
        for (ChunkPos chunkPos : selectedChunks) {
            if (!taggedChunks.containsKey(chunkPos)) {
                // Sound code removed
                // Notifications removed as requested
                return;
            }
        }

        // Get the player's town
        Town playerTown = null;
        if (client.player != null) {
            playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
        }

        if (playerTown == null) {
            Pokecobbleclaim.LOGGER.error("Cannot save chunks: player is not in a town");
            return;
        }

        // Check if the town has enough claim slots available
        int usedClaims = playerTown.getClaimCount();
        int maxClaims = playerTown.getMaxClaims();
        int availableClaims = maxClaims - usedClaims;

        if (selectedChunks.size() > availableClaims) {
            // Not enough claim slots available
            com.pokecobble.town.client.NotificationRenderer.addErrorNotification("Not enough claim slots available: " + usedClaims + "/" + maxClaims);
            return;
        }

        // Prepare data for network request
        List<ChunkPos> chunksToSave = new ArrayList<>(selectedChunks);
        Map<ChunkPos, ClaimTag> tagsToSave = new HashMap<>();

        // Add tags for each chunk
        for (ChunkPos chunkPos : chunksToSave) {
            ClaimTag tag = taggedChunks.get(chunkPos);
            if (tag != null) {
                tagsToSave.put(chunkPos, tag);
            }
        }

        // Send the claim request to the server
        com.pokecobble.town.network.chunk.ChunkNetworkHandler.requestChunkClaim(
            playerTown.getId(), chunksToSave, tagsToSave);

        // Log the claim
        Pokecobbleclaim.LOGGER.info("Saving " + chunksToSave.size() + " chunks to server for town " + playerTown.getName());
        for (ChunkPos chunkPos : chunksToSave) {
            ClaimTag tag = tagsToSave.get(chunkPos);
            Pokecobbleclaim.LOGGER.debug("Chunk claimed: " + chunkPos + " tag: " + (tag != null ? tag.getName() : "null"));

            // Update local chunk data
            updateChunkData(chunkPos, playerTown, tag);

            // Record claim action in history
            if (client.player != null) {
                playerTown.recordClaimAction(
                    ClaimHistoryEntry.ActionType.CLAIM,
                    chunkPos,
                    client.player.getUuid(),
                    client.player.getName().getString(),
                    tag
                );
            }
        }

        // Sound code removed

        // Notifications removed as requested

        // Clear the selected chunks
        selectedChunks.clear();

        // Force a redraw of the HUD by scheduling a task for the next tick
        if (client != null) {
            client.execute(() -> {
                // This will trigger a redraw of the HUD
                // We don't need to do anything here, just scheduling the task is enough
            });
        }
    }

    /**
     * Cycles to the next tag.
     */
    public void nextTag() {
        selectedTagIndex = (selectedTagIndex + 1) % claimTags.size();
        // Sound code removed
        // Notifications removed as requested

        // Force a redraw of the HUD by scheduling a task for the next tick
        if (client != null) {
            client.execute(() -> {
                // This will trigger a redraw of the HUD
                // We don't need to do anything here, just scheduling the task is enough
            });
        }
    }

    /**
     * Cycles to the previous tag.
     */
    public void previousTag() {
        selectedTagIndex = (selectedTagIndex - 1 + claimTags.size()) % claimTags.size();
        // Sound code removed
        // Notifications removed as requested

        // Force a redraw of the HUD by scheduling a task for the next tick
        if (client != null) {
            client.execute(() -> {
                // This will trigger a redraw of the HUD
                // We don't need to do anything here, just scheduling the task is enough
            });
        }
    }

    /**
     * Gets the current tag.
     *
     * @return The current tag
     */
    public ClaimTag getCurrentTag() {
        return claimTags.get(selectedTagIndex);
    }

    /**
     * Gets the tag for a chunk.
     *
     * @param chunkPos The chunk position
     * @return The tag for the chunk, or null if the chunk is not tagged
     */
    public ClaimTag getChunkTag(ChunkPos chunkPos) {
        return taggedChunks.get(chunkPos);
    }

    /**
     * Updates the claim tool's tag list with the provided tags.
     * This is called when tags are modified in the ClaimTagScreen.
     *
     * @param newTags The updated list of tags
     */
    public void updateTags(List<ClaimTag> newTags) {
        // Save the current selected tag index
        ClaimTag currentTag = null;
        if (selectedTagIndex >= 0 && selectedTagIndex < claimTags.size()) {
            currentTag = claimTags.get(selectedTagIndex);
        }

        // Clear the current tags
        claimTags.clear();

        // Add the new tags
        claimTags.addAll(newTags);

        // Try to restore the selected tag index
        if (currentTag != null) {
            // Find the tag with the same name
            for (int i = 0; i < claimTags.size(); i++) {
                if (claimTags.get(i).getName().equals(currentTag.getName())) {
                    selectedTagIndex = i;
                    break;
                }
            }
        }

        // Make sure the selected tag index is valid
        if (selectedTagIndex < 0 || selectedTagIndex >= claimTags.size()) {
            selectedTagIndex = 0;
        }

        // Update any tagged chunks with the new tags
        updateTaggedChunks();
    }

    /**
     * Checks if a chunk is selected.
     *
     * @param chunkPos The chunk position
     * @return true if the chunk is selected, false otherwise
     */
    public boolean isChunkSelected(ChunkPos chunkPos) {
        return selectedChunks.contains(chunkPos);
    }

    /**
     * Checks if a chunk is tagged.
     *
     * @param chunkPos The chunk position
     * @return true if the chunk is tagged, false otherwise
     */
    public boolean isChunkTagged(ChunkPos chunkPos) {
        return taggedChunks.containsKey(chunkPos);
    }

    /**
     * Checks if a chunk is claimed by another town.
     *
     * @param chunkPos The chunk position
     * @return true if the chunk is claimed by another town, false otherwise
     */
    public boolean isChunkClaimed(ChunkPos chunkPos) {
        return claimedChunks.containsKey(chunkPos);
    }

    /**
     * Checks if the town has reached its claim limit.
     *
     * @return true if the town has reached its claim limit, false otherwise
     */
    public boolean hasReachedClaimLimit() {
        // Get the player's town
        Town playerTown = null;
        if (client.player != null) {
            playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
        }

        if (playerTown != null) {
            int usedClaims = playerTown.getClaimCount();
            int maxClaims = playerTown.getMaxClaims();
            return usedClaims >= maxClaims;
        }

        return false;
    }

    /**
     * Sets a chunk as claimed by a town.
     *
     * @param chunkPos The chunk position
     * @param townName The name of the town
     */
    public void setChunkClaimed(ChunkPos chunkPos, String townName) {
        claimedChunks.put(chunkPos, townName);
    }

    /**
     * Clears a chunk's claimed status.
     *
     * @param chunkPos The chunk position
     */
    public void clearChunkClaimed(ChunkPos chunkPos) {
        claimedChunks.remove(chunkPos);
    }

    /**
     * Gets the name of the town that claimed a chunk.
     *
     * @param chunkPos The chunk position
     * @return The name of the town, or null if the chunk is not claimed
     */
    public String getChunkClaimedBy(ChunkPos chunkPos) {
        return claimedChunks.get(chunkPos);
    }

    /**
     * Checks if the claim tool is active.
     *
     * @return true if the claim tool is active, false otherwise
     */
    public boolean isActive() {
        return active;
    }

    /**
     * Checks if chunk boundaries are visible.
     *
     * @return true if chunk boundaries are visible, false otherwise
     */
    public boolean areChunksVisible() {
        return showChunks;
    }

    /**
     * Gets the list of claim tags.
     *
     * @return The list of claim tags
     */
    public List<ClaimTag> getClaimTags() {
        return claimTags;
    }

    /**
     * Gets the index of the selected tag.
     *
     * @return The index of the selected tag
     */
    public int getSelectedTagIndex() {
        return selectedTagIndex;
    }

    /**
     * Sets the index of the selected tag.
     *
     * @param index The index of the tag to select
     */
    public void setSelectedTagIndex(int index) {
        if (index >= 0 && index < claimTags.size()) {
            selectedTagIndex = index;
        }
    }

    /**
     * Gets the set of selected chunks.
     *
     * @return The set of selected chunks
     */
    public Set<ChunkPos> getSelectedChunks() {
        return selectedChunks;
    }

    /**
     * Sets a listener to be called when the selection changes.
     *
     * @param listener The listener to call
     */
    public void setSelectionChangeListener(Runnable listener) {
        this.selectionChangeListener = listener;
    }

    /**
     * Gets the map of tagged chunks.
     *
     * @return The map of tagged chunks
     */
    public Map<ChunkPos, ClaimTag> getTaggedChunks() {
        return taggedChunks;
    }

    /**
     * Gets the map of claimed chunks.
     *
     * @return The map of claimed chunks
     */
    public Map<ChunkPos, String> getClaimedChunks() {
        return claimedChunks;
    }

    /**
     * Updates the claim tool state.
     * This method is called every tick.
     */
    public void tick() {
        // Update the claimed chunks from the town manager
        updateClaimedChunks();

        // Update the player's current chunk
        updatePlayerChunk();
    }

    /**
     * Updates the claimed chunks from the town manager.
     */
    private void updateClaimedChunks() {
        // Clear the claimed chunks
        claimedChunks.clear();

        // Get the town manager
        TownManager townManager = TownManager.getInstance();

        // Get all towns
        Collection<Town> towns = townManager.getAllTowns();

        // Update the claimed chunks
        for (Town town : towns) {
            // Get the town's claimed chunks
            Collection<ChunkPos> townChunks = town.getClaimedChunks();

            // Add the town's claimed chunks to the map
            for (ChunkPos chunkPos : townChunks) {
                claimedChunks.put(chunkPos, town.getName());
            }
        }
    }

    /**
     * Updates the player's current chunk.
     */
    private void updatePlayerChunk() {
        // Get the player
        ClientPlayerEntity player = client.player;
        if (player == null) return;

        // Get the player's current chunk
        ChunkPos currentChunk = new ChunkPos(player.getBlockPos());

        // Update the chunk tracker
        ChunkTracker.setCurrentChunk(currentChunk);

        // Check if the player entered a claimed chunk
        if (isChunkClaimed(currentChunk)) {
            // Get the town that claimed the chunk
            String townName = getChunkClaimedBy(currentChunk);

            // Get the tag for the chunk
            ClaimTag tag = null;
            for (Town town : TownManager.getInstance().getAllTowns()) {
                if (town.getName().equals(townName)) {
                    // Get the tag for the chunk
                    tag = town.getChunkTag(currentChunk);
                    break;
                }
            }

            // Show a notification
            if (tag != null) {
                ClaimedChunkNotification.show(townName, tag.getName(), tag.getColor());
            } else {
                ClaimedChunkNotification.show(townName, "unknown", 0xFFFFFF);
            }
        } else {
            // Hide the notification
            ClaimedChunkNotification.hide();
        }
    }

    /**
     * Plays a sound at the player's position.
     *
     * @param sound The sound to play
     * @param volume The volume of the sound
     * @param pitch The pitch of the sound
     */
    private void playSound(net.minecraft.sound.SoundEvent sound, float volume, float pitch) {
        if (client.player != null) {
            client.getSoundManager().play(net.minecraft.client.sound.PositionedSoundInstance.master(sound, pitch, volume));
        }
    }

    /**
     * Handles mouse scrolling.
     *
     * @param vertical The vertical scroll amount
     */
    public void onMouseScroll(double vertical) {
        if (vertical > 0) {
            // Scroll up
            previousTag();
        } else if (vertical < 0) {
            // Scroll down
            nextTag();
        }
    }

    /**
     * Handles mouse scrolling.
     * This method is called by the event handler.
     *
     * @param vertical The vertical scroll amount
     */
    public void handleMouseScroll(double vertical) {
        onMouseScroll(vertical);
    }

    /**
     * Shows a confirmation dialog when exiting the claim tool.
     * This is called when the player presses ESC while the claim tool is active.
     * This will override any other screen that might be open.
     */
    public void showExitConfirmation() {
        // If the claim tool is not active, do nothing
        if (!active) return;

        // Always show the confirmation dialog, even if there are no selected chunks
        // This ensures the user always gets a confirmation before exiting

        // Show a confirmation dialog, regardless of whether a screen is already open
        // This will override any other screen
        Pokecobbleclaim.LOGGER.info("Showing exit confirmation dialog");

        // Customize message based on whether there are selected chunks
        String message;
        if (selectedChunks.isEmpty()) {
            message = "Are you sure you want to exit the claim tool?";
        } else {
            message = "Do you want to save your selected chunks before exiting?";
        }

        // Close any open screen first
        if (client.currentScreen != null) {
            Pokecobbleclaim.LOGGER.info("Closing screen: " + client.currentScreen.getClass().getSimpleName());
            client.setScreen(null);
        }

        if (selectedChunks.isEmpty()) {
            // If no chunks are selected, just show a simple Yes/No confirmation
            client.setScreen(new com.pokecobble.town.gui.ConfirmationScreen(
                null, // No parent screen
                "Exit Claim Tool?",
                message,
                // On confirm (Yes) - exit
                () -> {
                    toggle(); // Deactivate the claim tool
                },
                // On cancel (No) - stay in claim tool
                () -> {
                    // Do nothing, just close the confirmation screen
                }
            ));
        } else {
            // If chunks are selected, show save confirmation
            client.setScreen(new com.pokecobble.town.gui.ConfirmationScreen(
                null, // No parent screen
                "Save Changes?",
                message,
                // On confirm (Yes) - save and exit
                () -> {
                    // Save the changes
                    saveSelectedChunks();
                    toggle(); // Deactivate the claim tool
                },
                // On cancel (No) - exit without saving
                () -> {
                    toggle(); // Deactivate the claim tool
                }
            ));
        }
    }

    /**
     * Toggles the tag on the current chunk.
     * This is called when the player presses the tag key (default: E).
     * If the chunk is already selected, it will be deselected.
     */
    public void toggleTagOnCurrentChunk() {
        // If the claim tool is not active, do nothing
        if (!active) return;

        // Get the player's current chunk
        ClientPlayerEntity player = client.player;
        if (player == null) return;
        ChunkPos currentChunk = new ChunkPos(player.getBlockPos());

        // Tag or deselect the chunk
        tagChunk(currentChunk);
    }

    /**
     * Opens the claim tag menu.
     * This is called when the player presses the tag menu key (default: R).
     */
    public void openClaimTagMenu() {
        // If the claim tool is not active, do nothing
        if (!active) return;

        // Get the player's town
        if (client.player == null) return;

        Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
        if (playerTown == null) {
            // Player is not in a town
            // Notifications removed as requested
            return;
        }

        // Open the claim tag screen
        if (client != null) {
            client.setScreen(new com.pokecobble.town.gui.ClaimTagScreen(playerTown));
        }
    }

    /**
     * Activates the claim tool for a specific town.
     *
     * @param town The town to activate the claim tool for
     */
    public void activate(Town town) {
        // Activate the claim tool
        if (!active) {
            toggle();
        }

        // Close all screens
        if (client != null) {
            client.setScreen(null);
        }

        // Load the town's tags
        if (town != null) {
            // Request the latest claim count from the server
            // Note: We don't need to request town data here as it's already loaded

            List<ClaimTag> townTags = town.getClaimTags();
            if (townTags != null && !townTags.isEmpty()) {
                // Clear existing tags and add the town's tags
                claimTags.clear();
                claimTags.addAll(townTags);
                selectedTagIndex = 0; // Reset the selected tag index

                // Log the loaded tags
                Pokecobbleclaim.LOGGER.info("Loaded " + townTags.size() + " tags from town: " + town.getName());
                for (ClaimTag tag : townTags) {
                    Pokecobbleclaim.LOGGER.info("  - Tag: " + tag.getName() + " (Color: " + Integer.toHexString(tag.getColor()) + ")");
                }

                // Show claim usage notification
                int usedClaims = town.getClaimCount();
                int maxClaims = town.getMaxClaims();
                int availableClaims = maxClaims - usedClaims;
                com.pokecobble.town.client.NotificationRenderer.addNotification("Town claim usage: " + usedClaims + "/" + maxClaims + " (" + availableClaims + " available)");
            } else {
                // Town has no tags, initialize with defaults
                claimTags.clear();
                initClaimTags();

                // Save the default tags to the town
                town.updateClaimTags(claimTags);

                Pokecobbleclaim.LOGGER.info("Created default tags for town: " + town.getName());
            }
        }

        // Notifications removed as requested
    }



    /**
     * Updates the chunk data.
     * This is called when a chunk is claimed or when receiving chunk data from the server.
     *
     * @param chunkPos The chunk position
     * @param town The town that owns the chunk, or null if the chunk is unclaimed
     * @param tag The tag applied to the chunk, or null if the chunk has no tag
     */
    public void updateChunkData(ChunkPos chunkPos, Town town, ClaimTag tag) {
        if (town == null) {
            // Chunk is unclaimed
            clearChunkClaimed(chunkPos);
        } else {
            // Chunk is claimed
            setChunkClaimed(chunkPos, town.getName());
        }

        // Update the tag
        if (tag != null) {
            taggedChunks.put(chunkPos, tag);
        } else {
            taggedChunks.remove(chunkPos);
        }

        // Log the update
        Pokecobbleclaim.LOGGER.debug("Updated chunk data: " + chunkPos + " town: " + (town != null ? town.getName() : "null") + " tag: " + (tag != null ? tag.getName() : "null"));
    }

    /**
     * Updates tagged chunks after tag changes.
     * This ensures that all chunks have valid tags after tag modifications.
     */
    private void updateTaggedChunks() {
        // Create a map of tag names to tags
        Map<String, ClaimTag> tagMap = new HashMap<>();
        for (ClaimTag tag : claimTags) {
            tagMap.put(tag.getName(), tag);
        }

        // Update all tagged chunks
        Map<ChunkPos, ClaimTag> updatedTags = new HashMap<>();
        for (Map.Entry<ChunkPos, ClaimTag> entry : taggedChunks.entrySet()) {
            ChunkPos chunkPos = entry.getKey();
            ClaimTag oldTag = entry.getValue();

            // Find the updated tag with the same name
            ClaimTag newTag = tagMap.get(oldTag.getName());
            if (newTag != null) {
                updatedTags.put(chunkPos, newTag);
            } else {
                // If the tag no longer exists, use the first available tag
                if (!claimTags.isEmpty()) {
                    updatedTags.put(chunkPos, claimTags.get(0));
                }
                // If no tags are available, the chunk will be untagged
            }
        }

        // Replace the tagged chunks map
        taggedChunks.clear();
        taggedChunks.putAll(updatedTags);
    }

    /**
     * Checks if expanded help is shown.
     *
     * @return true if expanded help is shown, false otherwise
     */
    public boolean isShowExpandedHelp() {
        // Never show expanded help - it's been removed
        return false;
    }

    /**
     * Refreshes chunk data for a specific chunk.
     * This is called when chunk data is updated from the server.
     *
     * @param chunkPos The chunk position to refresh
     */
    public void refreshChunkData(ChunkPos chunkPos) {
        if (chunkPos == null) {
            return;
        }

        // Update the claimed chunks from the chunk permission manager
        ChunkPermissionManager permissionManager = ChunkPermissionManager.getInstance();
        Town town = permissionManager.getTownForChunk(chunkPos);

        if (town != null) {
            // Chunk is claimed
            setChunkClaimed(chunkPos, town.getName());

            // Update tag if present
            ClaimTag tag = permissionManager.getTagForChunk(chunkPos);
            if (tag != null) {
                taggedChunks.put(chunkPos, tag);
            } else {
                taggedChunks.remove(chunkPos);
            }
        } else {
            // Chunk is not claimed
            clearChunkClaimed(chunkPos);
            taggedChunks.remove(chunkPos);
        }

        Pokecobbleclaim.LOGGER.debug("Refreshed chunk data for " + chunkPos);
    }

    // All chunk rendering code has been removed
}
