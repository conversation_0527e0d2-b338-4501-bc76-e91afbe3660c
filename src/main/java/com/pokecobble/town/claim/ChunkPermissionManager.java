package com.pokecobble.town.claim;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages chunk permissions and handles permission checks efficiently.
 * This is a stub implementation that will be replaced with a proper implementation later.
 */
public class ChunkPermissionManager {
    private static ChunkPermissionManager instance;

    // Cache of chunk permissions - stores the tag for each chunk
    private final Map<ChunkPos, ClaimTag> chunkTags = new ConcurrentHashMap<>();

    // Cache of town ownership - stores which town owns each chunk
    private final Map<ChunkPos, Town> chunkOwners = new ConcurrentHashMap<>();

    private ChunkPermissionManager() {
        // Private constructor for singleton
    }

    /**
     * Gets the singleton instance of the ChunkPermissionManager.
     */
    public static ChunkPermissionManager getInstance() {
        if (instance == null) {
            instance = new ChunkPermissionManager();
        }
        return instance;
    }

    /**
     * Saves chunk tags to the server.
     *
     * @param town The town that owns the chunks
     * @param chunkTags Map of chunks to their tags
     */
    public void saveChunkTags(Town town, Map<ChunkPos, ClaimTag> chunkTags) {
        // Update local data
        for (Map.Entry<ChunkPos, ClaimTag> entry : chunkTags.entrySet()) {
            ChunkPos chunk = entry.getKey();
            ClaimTag tag = entry.getValue();

            this.chunkTags.put(chunk, tag);
            this.chunkOwners.put(chunk, town);

            // Trigger permission synchronization if server is available
            try {
                net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
                if (server != null) {
                    com.pokecobble.town.permission.PermissionChangeTracker.getInstance()
                        .trackChunkPermissionChange(server, chunk, tag);
                }
            } catch (Exception e) {
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error triggering chunk permission sync: " + e.getMessage());
            }
        }

        // Mark all chunks as changed for synchronization
        ChunkDataSynchronizer.markChunksChanged(chunkTags.keySet());
    }

    /**
     * Checks if a player has permission to perform an action in a chunk.
     *
     * @param player The player to check
     * @param world The world
     * @param pos The block position
     * @param action The action to check (0=build, 1=interact, 2=containers, etc.)
     * @return True if the player has permission, false otherwise
     */
    public boolean hasPermission(PlayerEntity player, World world, BlockPos pos, int action) {
        // Get the chunk position
        ChunkPos chunkPos = new ChunkPos(pos);

        // Get the town that owns the chunk
        Town town = getTownForChunk(chunkPos);

        if (town == null) {
            // No town owns this chunk, allow all actions
            return true;
        }

        // Check if the player is in the town
        if (town.getPlayers().contains(player.getUuid())) {
            // Player is in the town, check their rank
            TownPlayerRank rank = town.getPlayerRank(player.getUuid());

            // Get the tag for the chunk
            ClaimTag tag = getTagForChunk(chunkPos);

            if (tag == null) {
                // No tag, allow all actions for town members
                return true;
            }

            // Check if the player's rank has permission for this action
            return tag.getRankPermissions().hasPermission(rank, action);
        } else {
            // Player is not in the town, check non-member permissions
            ClaimTag tag = getTagForChunk(chunkPos);

            if (tag == null) {
                // No tag, deny all actions for non-members
                return false;
            }

            // Check if non-members have permission for this action
            return tag.getRankPermissions().hasPermission(null, action);
        }
    }

    /**
     * Notifies a player that they don't have permission to perform an action.
     *
     * @param player The player to notify
     * @param townName The name of the town
     */
    public void notifyPermissionDenied(PlayerEntity player, String townName) {
        player.sendMessage(Text.literal("§cYou don't have permission to do this in " + townName + "."), false);
    }

    /**
     * Gets the town that owns a chunk.
     *
     * @param chunkPos The chunk position
     * @return The town that owns the chunk, or null if no town owns it
     */
    public Town getTownForChunk(ChunkPos chunkPos) {
        return chunkOwners.get(chunkPos);
    }

    /**
     * Gets the tag for a chunk.
     *
     * @param chunkPos The chunk position
     * @return The tag for the chunk, or null if no tag is set
     */
    public ClaimTag getTagForChunk(ChunkPos chunkPos) {
        return chunkTags.get(chunkPos);
    }

    /**
     * Sets the tag for a chunk.
     *
     * @param chunk The chunk position
     * @param town The town that owns the chunk
     * @param tag The tag to apply to the chunk
     */
    public void setChunkTag(ChunkPos chunk, Town town, ClaimTag tag) {
        // Update local data
        chunkTags.put(chunk, tag);
        chunkOwners.put(chunk, town);

        // Mark the chunk as changed for synchronization
        ChunkDataSynchronizer.markChunkChanged(chunk);
    }

    /**
     * Removes a chunk claim.
     * This is used when a chunk is unclaimed.
     *
     * @param chunkPos The chunk position to remove
     */
    public void removeChunkClaim(ChunkPos chunkPos) {
        chunkTags.remove(chunkPos);
        chunkOwners.remove(chunkPos);

        // Mark the chunk as changed for synchronization
        ChunkDataSynchronizer.markChunkChanged(chunkPos);
    }
}