package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side player data manager.
 * Manages cached player data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientPlayerManager {
    private static ClientPlayerManager instance;
    
    // Cached player data
    private final Map<UUID, TownPlayer> players = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> playerVersions = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Current player data cache
    private UUID currentPlayerId = null;
    private TownPlayer currentPlayerData = null;
    private int currentPlayerVersion = -1;
    
    // Player permissions cache
    private final Map<UUID, Map<String, Map<String, Boolean>>> playerPermissions = new ConcurrentHashMap<>();
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 30000; // 30 seconds
    
    private ClientPlayerManager() {}
    
    public static ClientPlayerManager getInstance() {
        if (instance == null) {
            instance = new ClientPlayerManager();
        }
        return instance;
    }
    
    /**
     * Updates a player in the cache.
     */
    public void updatePlayer(UUID playerId, TownPlayer player, int version) {
        if (playerId == null || player == null) {
            return;
        }
        
        int currentVersion = playerVersions.getOrDefault(playerId, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            players.put(playerId, player);
            playerVersions.put(playerId, version);
            lastUpdateTimes.put(playerId, System.currentTimeMillis());
            
            // Update current player cache if this is the current player
            if (playerId.equals(currentPlayerId)) {
                currentPlayerData = player;
                currentPlayerVersion = version;
            }
            
            Pokecobbleclaim.LOGGER.debug("Updated player cache for " + player.getName() + " (version " + version + ")");
        }
    }
    
    /**
     * Gets a player from the cache.
     */
    public TownPlayer getPlayer(UUID playerId) {
        if (playerId == null) {
            return null;
        }
        
        // Check if cache is expired
        Long lastUpdate = lastUpdateTimes.get(playerId);
        if (lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS) {
            // Cache expired, remove from cache
            invalidatePlayer(playerId);
            return null;
        }
        
        return players.get(playerId);
    }
    
    /**
     * Gets the version of a cached player.
     */
    public int getPlayerVersion(UUID playerId) {
        return playerVersions.getOrDefault(playerId, -1);
    }
    
    /**
     * Invalidates a player from the cache.
     */
    public void invalidatePlayer(UUID playerId) {
        players.remove(playerId);
        playerVersions.remove(playerId);
        lastUpdateTimes.remove(playerId);
        playerPermissions.remove(playerId);
        
        // Clear current player cache if this is the current player
        if (playerId.equals(currentPlayerId)) {
            currentPlayerData = null;
            currentPlayerVersion = -1;
        }
    }
    
    /**
     * Sets the current player data.
     */
    public void setCurrentPlayer(UUID playerId, TownPlayer player, int version) {
        this.currentPlayerId = playerId;
        this.currentPlayerData = player;
        this.currentPlayerVersion = version;
        
        // Also update in the general cache
        if (player != null) {
            updatePlayer(playerId, player, version);
        }
    }
    
    /**
     * Gets the current player ID.
     */
    public UUID getCurrentPlayerId() {
        return currentPlayerId;
    }
    
    /**
     * Gets the current player data.
     */
    public TownPlayer getCurrentPlayer() {
        return currentPlayerData;
    }
    
    /**
     * Gets the current player version.
     */
    public int getCurrentPlayerVersion() {
        return currentPlayerVersion;
    }
    
    /**
     * Updates player permissions.
     */
    public void updatePlayerPermissions(UUID playerId, Map<String, Map<String, Boolean>> permissions) {
        if (playerId != null && permissions != null) {
            playerPermissions.put(playerId, new HashMap<>(permissions));
        }
    }
    
    /**
     * Gets player permissions.
     */
    public Map<String, Map<String, Boolean>> getPlayerPermissions(UUID playerId) {
        Map<String, Map<String, Boolean>> permissions = playerPermissions.get(playerId);
        return permissions != null ? new HashMap<>(permissions) : new HashMap<>();
    }
    
    /**
     * Checks if a player has a specific permission.
     */
    public boolean hasPermission(UUID playerId, String category, String permission) {
        Map<String, Map<String, Boolean>> permissions = playerPermissions.get(playerId);
        if (permissions == null) {
            return false;
        }
        
        Map<String, Boolean> categoryPermissions = permissions.get(category);
        if (categoryPermissions == null) {
            return false;
        }
        
        return categoryPermissions.getOrDefault(permission, false);
    }
    
    /**
     * Gets the player's rank.
     */
    public TownPlayerRank getPlayerRank(UUID playerId) {
        TownPlayer player = getPlayer(playerId);
        return player != null ? player.getRank() : TownPlayerRank.MEMBER;
    }
    
    /**
     * Checks if the current player has a specific permission.
     */
    public boolean currentPlayerHasPermission(String category, String permission) {
        if (currentPlayerId == null) {
            return false;
        }
        return hasPermission(currentPlayerId, category, permission);
    }
    
    /**
     * Gets the current player's rank.
     */
    public TownPlayerRank getCurrentPlayerRank() {
        return currentPlayerData != null ? currentPlayerData.getRank() : TownPlayerRank.MEMBER;
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        players.clear();
        playerVersions.clear();
        lastUpdateTimes.clear();
        playerPermissions.clear();
        currentPlayerId = null;
        currentPlayerData = null;
        currentPlayerVersion = -1;
        
        Pokecobbleclaim.LOGGER.debug("Cleared player cache");
    }
    
    /**
     * Checks if a player needs to be refreshed from the server.
     */
    public boolean needsRefresh(UUID playerId) {
        Long lastUpdate = lastUpdateTimes.get(playerId);
        return lastUpdate == null || System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS;
    }
    
    /**
     * Gets all cached players.
     */
    public Collection<TownPlayer> getAllPlayers() {
        return new ArrayList<>(players.values());
    }
    
    /**
     * Gets all cached player IDs.
     */
    public Set<UUID> getAllPlayerIds() {
        return new HashSet<>(players.keySet());
    }

    /**
     * Updates a player's rank in the cache.
     */
    public void updatePlayerRank(UUID playerId, TownPlayerRank rank, int version) {
        if (playerId == null || rank == null) {
            return;
        }

        TownPlayer player = getPlayer(playerId);
        if (player != null) {
            // Create a new player object with updated rank
            TownPlayer updatedPlayer = new TownPlayer(player.getUuid(), player.getName(), rank, player.isOnline());

            // Copy permissions from the old player
            Map<String, Map<String, Boolean>> allPermissions = player.getAllPermissions();
            for (Map.Entry<String, Map<String, Boolean>> entry : allPermissions.entrySet()) {
                updatedPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
            }

            // Update the player in cache
            updatePlayer(playerId, updatedPlayer, version);

            Pokecobbleclaim.LOGGER.debug("Updated player rank for " + player.getName() + " to " + rank.name());
        }
    }

    /**
     * Updates a player's permission in the cache.
     */
    public void updatePlayerPermission(UUID playerId, String permissionCategory, String permissionName, boolean value, int version) {
        if (playerId == null || permissionCategory == null || permissionName == null) {
            return;
        }

        TownPlayer player = getPlayer(playerId);
        if (player != null) {
            // Update the permission
            Map<String, Boolean> categoryPermissions = player.getCategoryPermissions(permissionCategory);
            if (categoryPermissions == null) {
                categoryPermissions = new HashMap<>();
            }
            categoryPermissions.put(permissionName, value);
            player.setCategoryPermissions(permissionCategory, categoryPermissions);

            // Update the player in cache with new version
            updatePlayer(playerId, player, version);

            Pokecobbleclaim.LOGGER.debug("Updated permission " + permissionName + " in category " + permissionCategory +
                                       " for player " + player.getName() + " to " + value);
        }
    }
}
