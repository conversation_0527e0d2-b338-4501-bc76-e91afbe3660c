package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.invitation.TownInvitation;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side invitation manager.
 * Manages cached invitation data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientInvitationManager {
    private static ClientInvitationManager instance;
    
    // Cached invitation data
    private final Map<UUID, TownInvitation> invitations = new ConcurrentHashMap<>();
    private final Map<UUID, String> invitationTownNames = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Version tracking
    private int invitationVersion = -1;
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 30000; // 30 seconds
    
    private ClientInvitationManager() {}
    
    public static ClientInvitationManager getInstance() {
        if (instance == null) {
            instance = new ClientInvitationManager();
        }
        return instance;
    }
    
    /**
     * Updates an invitation in the cache.
     */
    public void updateInvitation(TownInvitation invitation, String townName, int version) {
        if (invitation == null) {
            return;
        }
        
        // Only update if version is newer
        if (version > invitationVersion) {
            invitations.put(invitation.getId(), invitation);
            invitationTownNames.put(invitation.getId(), townName);
            lastUpdateTimes.put(invitation.getId(), System.currentTimeMillis());
            
            invitationVersion = version;
            
            Pokecobbleclaim.LOGGER.debug("Updated invitation cache for " + invitation.getId() + " (version " + version + ")");
        }
    }
    
    /**
     * Gets an invitation from the cache.
     */
    public TownInvitation getInvitation(UUID invitationId) {
        if (invitationId == null) {
            return null;
        }
        
        // Check if cache is expired
        Long lastUpdate = lastUpdateTimes.get(invitationId);
        if (lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS) {
            // Cache expired, remove from cache
            invalidateInvitation(invitationId);
            return null;
        }
        
        return invitations.get(invitationId);
    }
    
    /**
     * Gets the town name for an invitation.
     */
    public String getInvitationTownName(UUID invitationId) {
        return invitationTownNames.get(invitationId);
    }
    
    /**
     * Gets all cached invitations.
     */
    public List<TownInvitation> getAllInvitations() {
        List<TownInvitation> result = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        
        for (Map.Entry<UUID, TownInvitation> entry : invitations.entrySet()) {
            UUID invitationId = entry.getKey();
            TownInvitation invitation = entry.getValue();
            
            // Check if cache is expired
            Long lastUpdate = lastUpdateTimes.get(invitationId);
            if (lastUpdate != null && currentTime - lastUpdate > CACHE_TIMEOUT_MS) {
                // Cache expired, skip this invitation
                continue;
            }
            
            // Skip expired invitations
            if (invitation.isExpired()) {
                continue;
            }
            
            result.add(invitation);
        }
        
        return result;
    }
    
    /**
     * Gets all pending invitations.
     */
    public List<TownInvitation> getPendingInvitations() {
        return getAllInvitations().stream()
                .filter(TownInvitation::isPending)
                .sorted(Comparator.comparing(TownInvitation::getCreatedTime))
                .toList();
    }
    
    /**
     * Gets the number of pending invitations.
     */
    public int getPendingInvitationCount() {
        return getPendingInvitations().size();
    }
    
    /**
     * Checks if there are any pending invitations.
     */
    public boolean hasPendingInvitations() {
        return getPendingInvitationCount() > 0;
    }
    
    /**
     * Gets invitations from a specific town.
     */
    public List<TownInvitation> getInvitationsFromTown(UUID townId) {
        if (townId == null) {
            return new ArrayList<>();
        }
        
        return getAllInvitations().stream()
                .filter(invitation -> townId.equals(invitation.getFromTownId()))
                .toList();
    }
    
    /**
     * Checks if there's a pending invitation from a specific town.
     */
    public boolean hasPendingInvitationFromTown(UUID townId) {
        return getInvitationsFromTown(townId).stream()
                .anyMatch(TownInvitation::isPending);
    }
    
    /**
     * Gets the most recent invitation.
     */
    public TownInvitation getMostRecentInvitation() {
        return getAllInvitations().stream()
                .max(Comparator.comparing(TownInvitation::getCreatedTime))
                .orElse(null);
    }
    
    /**
     * Gets invitations that are expiring soon (within 1 minute).
     */
    public List<TownInvitation> getExpiringSoonInvitations() {
        long oneMinute = 60000; // 1 minute in milliseconds
        
        return getAllInvitations().stream()
                .filter(invitation -> invitation.isPending() && invitation.getTimeRemaining() < oneMinute)
                .toList();
    }
    
    /**
     * Invalidates an invitation from the cache.
     */
    public void invalidateInvitation(UUID invitationId) {
        invitations.remove(invitationId);
        invitationTownNames.remove(invitationId);
        lastUpdateTimes.remove(invitationId);
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        invitations.clear();
        invitationTownNames.clear();
        lastUpdateTimes.clear();
        invitationVersion = -1;
        
        Pokecobbleclaim.LOGGER.debug("Cleared invitation cache");
    }
    
    /**
     * Gets the current invitation version.
     */
    public int getInvitationVersion() {
        return invitationVersion;
    }
    
    /**
     * Checks if invitations need to be refreshed from the server.
     */
    public boolean needsRefresh() {
        // Check if we have any cached data
        if (invitations.isEmpty()) {
            return true;
        }
        
        // Check if any cached data is expired
        long currentTime = System.currentTimeMillis();
        for (Long lastUpdate : lastUpdateTimes.values()) {
            if (currentTime - lastUpdate > CACHE_TIMEOUT_MS) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Accepts an invitation.
     */
    public void acceptInvitation(UUID invitationId) {
        TownInvitation invitation = getInvitation(invitationId);
        if (invitation != null && invitation.isPending()) {
            // Send accept to server
            com.pokecobble.town.invitation.TownInvitationSynchronizer.sendInvitationAccept(invitationId);
            
            // Mark as accepted locally (will be updated by server sync)
            invitation.accept();
            
            Pokecobbleclaim.LOGGER.info("Accepted invitation " + invitationId);
        }
    }
    
    /**
     * Declines an invitation.
     */
    public void declineInvitation(UUID invitationId) {
        TownInvitation invitation = getInvitation(invitationId);
        if (invitation != null && invitation.isPending()) {
            // Send decline to server
            com.pokecobble.town.invitation.TownInvitationSynchronizer.sendInvitationDecline(invitationId);
            
            // Mark as declined locally (will be updated by server sync)
            invitation.decline();
            
            Pokecobbleclaim.LOGGER.info("Declined invitation " + invitationId);
        }
    }
    
    /**
     * Requests invitation sync from server.
     */
    public void requestSync() {
        com.pokecobble.town.invitation.TownInvitationSynchronizer.requestInvitationSync();
    }
    
    /**
     * Gets cache statistics.
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedInvitations", invitations.size());
        stats.put("pendingInvitations", getPendingInvitationCount());
        stats.put("invitationVersion", invitationVersion);
        stats.put("expiringSoon", getExpiringSoonInvitations().size());
        
        // Count invitations by status
        Map<String, Integer> statusCounts = new HashMap<>();
        for (TownInvitation invitation : getAllInvitations()) {
            String status = invitation.getStatus().name();
            statusCounts.put(status, statusCounts.getOrDefault(status, 0) + 1);
        }
        stats.put("statusCounts", statusCounts);
        
        return stats;
    }
}
