package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.claim.ChunkPermissionManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.math.ChunkPos;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handles real-time data synchronization for the claim tool HUD.
 * Ensures the HUD displays up-to-date information about claims, permissions, and town data.
 */
@Environment(EnvType.CLIENT)
public class ClaimToolDataSynchronizer {
    private static ClaimToolDataSynchronizer instance;
    
    // Cached data for the claim tool
    private final Map<ChunkPos, ClaimData> chunkDataCache = new ConcurrentHashMap<>();
    private final Map<UUID, TownClaimInfo> townClaimInfoCache = new ConcurrentHashMap<>();
    
    // Update tracking
    private long lastFullUpdate = 0;
    private long lastChunkUpdate = 0;
    private long lastTownUpdate = 0;
    
    // Configuration
    private static final long FULL_UPDATE_INTERVAL_MS = 5000; // 5 seconds
    private static final long CHUNK_UPDATE_INTERVAL_MS = 1000; // 1 second
    private static final long TOWN_UPDATE_INTERVAL_MS = 2000; // 2 seconds
    
    // Event listeners
    private final Set<Runnable> dataChangeListeners = new HashSet<>();
    
    private ClaimToolDataSynchronizer() {
        setupEventHandlers();
    }
    
    public static ClaimToolDataSynchronizer getInstance() {
        if (instance == null) {
            instance = new ClaimToolDataSynchronizer();
        }
        return instance;
    }
    
    /**
     * Sets up event handlers for data synchronization.
     */
    private void setupEventHandlers() {
        ClientSyncManager syncManager = ClientSyncManager.getInstance();
        
        // Town data updates
        syncManager.addEventHandler("town_data_updated", (data) -> {
            invalidateTownCache();
            notifyDataChange();
        });
        
        // Chunk data updates
        syncManager.addEventHandler("chunk_data_updated", (data) -> {
            invalidateChunkCache();
            notifyDataChange();
        });
        
        // Permission updates
        syncManager.addEventHandler("permission_updated", (data) -> {
            invalidateAllCaches();
            notifyDataChange();
        });
        
        // Chunk permission updates
        syncManager.addEventHandler("chunk_permission_updated", (data) -> {
            if (data instanceof Map) {
                Map<String, Object> eventData = (Map<String, Object>) data;
                ChunkPos chunkPos = (ChunkPos) eventData.get("chunkPos");
                if (chunkPos != null) {
                    invalidateChunkData(chunkPos);
                }
            }
            notifyDataChange();
        });
        
        Pokecobbleclaim.LOGGER.debug("Set up claim tool data sync event handlers");
    }
    
    /**
     * Updates the claim tool data if needed.
     */
    public void updateIfNeeded() {
        long currentTime = System.currentTimeMillis();
        
        // Check if we need a full update
        if (currentTime - lastFullUpdate > FULL_UPDATE_INTERVAL_MS) {
            performFullUpdate();
            lastFullUpdate = currentTime;
        }
        // Check if we need a chunk update
        else if (currentTime - lastChunkUpdate > CHUNK_UPDATE_INTERVAL_MS) {
            updateChunkData();
            lastChunkUpdate = currentTime;
        }
        // Check if we need a town update
        else if (currentTime - lastTownUpdate > TOWN_UPDATE_INTERVAL_MS) {
            updateTownData();
            lastTownUpdate = currentTime;
        }
    }
    
    /**
     * Performs a full data update.
     */
    private void performFullUpdate() {
        try {
            updateTownData();
            updateChunkData();
            updateVisibleChunks();
            
            Pokecobbleclaim.LOGGER.debug("Performed full claim tool data update");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during full claim tool data update: " + e.getMessage());
        }
    }
    
    /**
     * Updates town-related data.
     */
    private void updateTownData() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                return;
            }
            
            // Get player's town
            Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
            if (playerTown != null) {
                TownClaimInfo info = new TownClaimInfo(
                    playerTown.getId(),
                    playerTown.getName(),
                    playerTown.getClaimCount(),
                    playerTown.getMaxClaims(),
                    playerTown.getPlayerCount(),
                    System.currentTimeMillis()
                );
                
                townClaimInfoCache.put(playerTown.getId(), info);
            }
            
            // Update all visible towns
            for (Town town : TownManager.getInstance().getAllTowns()) {
                if (!townClaimInfoCache.containsKey(town.getId()) || 
                    System.currentTimeMillis() - townClaimInfoCache.get(town.getId()).lastUpdate > TOWN_UPDATE_INTERVAL_MS) {
                    
                    TownClaimInfo info = new TownClaimInfo(
                        town.getId(),
                        town.getName(),
                        town.getClaimCount(),
                        town.getMaxClaims(),
                        town.getPlayerCount(),
                        System.currentTimeMillis()
                    );
                    
                    townClaimInfoCache.put(town.getId(), info);
                }
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating town data: " + e.getMessage());
        }
    }
    
    /**
     * Updates chunk-related data.
     */
    private void updateChunkData() {
        try {
            // Update data for chunks around the player
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                return;
            }
            
            ChunkPos playerChunk = new ChunkPos(client.player.getBlockPos());
            int radius = 5; // Update chunks within 5 chunk radius
            
            for (int x = playerChunk.x - radius; x <= playerChunk.x + radius; x++) {
                for (int z = playerChunk.z - radius; z <= playerChunk.z + radius; z++) {
                    ChunkPos chunkPos = new ChunkPos(x, z);
                    updateChunkData(chunkPos);
                }
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating chunk data: " + e.getMessage());
        }
    }
    
    /**
     * Updates data for a specific chunk.
     */
    private void updateChunkData(ChunkPos chunkPos) {
        try {
            // Get chunk ownership
            Town owningTown = ChunkPermissionManager.getInstance().getTownForChunk(chunkPos);
            ClaimTag tag = ChunkPermissionManager.getInstance().getTagForChunk(chunkPos);
            
            ClaimData data = new ClaimData(
                chunkPos,
                owningTown != null ? owningTown.getId() : null,
                owningTown != null ? owningTown.getName() : null,
                tag,
                System.currentTimeMillis()
            );
            
            chunkDataCache.put(chunkPos, data);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating chunk data for " + chunkPos + ": " + e.getMessage());
        }
    }
    
    /**
     * Updates visible chunks based on claim tool selection.
     */
    private void updateVisibleChunks() {
        try {
            ClaimTool claimTool = ClaimTool.getInstance();
            if (!claimTool.isActive()) {
                return;
            }
            
            // Update selected chunks
            for (ChunkPos chunkPos : claimTool.getSelectedChunks()) {
                updateChunkData(chunkPos);
            }
            
            // Update tagged chunks
            for (ChunkPos chunkPos : claimTool.getTaggedChunks().keySet()) {
                updateChunkData(chunkPos);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating visible chunks: " + e.getMessage());
        }
    }
    
    /**
     * Gets cached claim data for a chunk.
     */
    public ClaimData getChunkData(ChunkPos chunkPos) {
        ClaimData data = chunkDataCache.get(chunkPos);
        
        // If data is stale, update it
        if (data == null || System.currentTimeMillis() - data.lastUpdate > CHUNK_UPDATE_INTERVAL_MS * 2) {
            updateChunkData(chunkPos);
            data = chunkDataCache.get(chunkPos);
        }
        
        return data;
    }
    
    /**
     * Gets cached town claim info.
     */
    public TownClaimInfo getTownClaimInfo(UUID townId) {
        TownClaimInfo info = townClaimInfoCache.get(townId);
        
        // If data is stale, update it
        if (info == null || System.currentTimeMillis() - info.lastUpdate > TOWN_UPDATE_INTERVAL_MS * 2) {
            Town town = TownManager.getInstance().getTownById(townId);
            if (town != null) {
                info = new TownClaimInfo(
                    town.getId(),
                    town.getName(),
                    town.getClaimCount(),
                    town.getMaxClaims(),
                    town.getPlayerCount(),
                    System.currentTimeMillis()
                );
                townClaimInfoCache.put(townId, info);
            }
        }
        
        return info;
    }
    
    /**
     * Gets the player's current town claim info.
     */
    public TownClaimInfo getPlayerTownClaimInfo() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            return null;
        }
        
        Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
        if (playerTown == null) {
            return null;
        }
        
        return getTownClaimInfo(playerTown.getId());
    }
    
    /**
     * Adds a data change listener.
     */
    public void addDataChangeListener(Runnable listener) {
        dataChangeListeners.add(listener);
    }
    
    /**
     * Removes a data change listener.
     */
    public void removeDataChangeListener(Runnable listener) {
        dataChangeListeners.remove(listener);
    }
    
    /**
     * Notifies all listeners of data changes.
     */
    private void notifyDataChange() {
        for (Runnable listener : dataChangeListeners) {
            try {
                listener.run();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error in data change listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * Invalidates all cached data.
     */
    public void invalidateAllCaches() {
        chunkDataCache.clear();
        townClaimInfoCache.clear();
        lastFullUpdate = 0;
        lastChunkUpdate = 0;
        lastTownUpdate = 0;
    }
    
    /**
     * Invalidates chunk cache.
     */
    public void invalidateChunkCache() {
        chunkDataCache.clear();
        lastChunkUpdate = 0;
    }
    
    /**
     * Invalidates town cache.
     */
    public void invalidateTownCache() {
        townClaimInfoCache.clear();
        lastTownUpdate = 0;
    }
    
    /**
     * Invalidates data for a specific chunk.
     */
    public void invalidateChunkData(ChunkPos chunkPos) {
        chunkDataCache.remove(chunkPos);
    }
    
    /**
     * Forces an immediate update of all data.
     */
    public void forceUpdate() {
        invalidateAllCaches();
        performFullUpdate();
        notifyDataChange();
    }
    
    /**
     * Represents cached claim data for a chunk.
     */
    public static class ClaimData {
        public final ChunkPos chunkPos;
        public final UUID owningTownId;
        public final String owningTownName;
        public final ClaimTag tag;
        public final long lastUpdate;
        
        public ClaimData(ChunkPos chunkPos, UUID owningTownId, String owningTownName, ClaimTag tag, long lastUpdate) {
            this.chunkPos = chunkPos;
            this.owningTownId = owningTownId;
            this.owningTownName = owningTownName;
            this.tag = tag;
            this.lastUpdate = lastUpdate;
        }
        
        public boolean isClaimed() {
            return owningTownId != null;
        }
        
        public boolean hasTag() {
            return tag != null;
        }
    }
    
    /**
     * Represents cached town claim information.
     */
    public static class TownClaimInfo {
        public final UUID townId;
        public final String townName;
        public final int claimCount;
        public final int maxClaims;
        public final int playerCount;
        public final long lastUpdate;
        
        public TownClaimInfo(UUID townId, String townName, int claimCount, int maxClaims, int playerCount, long lastUpdate) {
            this.townId = townId;
            this.townName = townName;
            this.claimCount = claimCount;
            this.maxClaims = maxClaims;
            this.playerCount = playerCount;
            this.lastUpdate = lastUpdate;
        }
        
        public int getAvailableClaims() {
            return Math.max(0, maxClaims - claimCount);
        }
        
        public boolean isAtClaimLimit() {
            return claimCount >= maxClaims;
        }
        
        public boolean isNearClaimLimit() {
            return claimCount >= maxClaims - 1;
        }
    }
}
