package com.pokecobble.town.client;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.Map;

/**
 * Client-side command for testing and validating synchronization functionality.
 */
@Environment(EnvType.CLIENT)
public class ClientSyncTestCommand {
    
    public static void register(CommandDispatcher<FabricClientCommandSource> dispatcher) {
        dispatcher.register(ClientCommandManager.literal("clientsync")
            .then(ClientCommandManager.literal("status")
                .executes(ClientSyncTestCommand::showClientSyncStatus))
            .then(ClientCommandManager.literal("stats")
                .executes(ClientSyncTestCommand::showClientStats))
            .then(ClientCommandManager.literal("clear")
                .executes(ClientSyncTestCommand::clearClientCache))
            .then(ClientCommandManager.literal("test")
                .executes(ClientSyncTestCommand::runClientSyncTest))
        );
    }
    
    /**
     * Shows client-side synchronization status.
     */
    private static int showClientSyncStatus(CommandContext<FabricClientCommandSource> context) {
        FabricClientCommandSource source = context.getSource();
        MinecraftClient client = source.getClient();
        
        try {
            if (client.player == null) {
                source.sendError(Text.literal("Player not available"));
                return 0;
            }
            
            ClientSyncManager syncManager = ClientSyncManager.getInstance();
            
            source.sendFeedback(Text.literal("=== Client Synchronization Status ===").formatted(Formatting.GOLD));
            source.sendFeedback(Text.literal("Player: " + client.player.getName().getString()).formatted(Formatting.YELLOW));
            
            // Show sync times
            source.sendFeedback(Text.literal("Last Sync Times:").formatted(Formatting.YELLOW));
            
            long townSyncTime = syncManager.getTimeSinceLastSync(ClientSyncManager.EVENT_TOWN_UPDATED);
            source.sendFeedback(Text.literal("  Town Data: " + formatTime(townSyncTime)).formatted(Formatting.WHITE));
            
            long playerSyncTime = syncManager.getTimeSinceLastSync(ClientSyncManager.EVENT_PLAYER_UPDATED);
            source.sendFeedback(Text.literal("  Player Data: " + formatTime(playerSyncTime)).formatted(Formatting.WHITE));
            
            long chunkSyncTime = syncManager.getTimeSinceLastSync(ClientSyncManager.EVENT_CHUNK_UPDATED);
            source.sendFeedback(Text.literal("  Chunk Data: " + formatTime(chunkSyncTime)).formatted(Formatting.WHITE));
            
            long moneySyncTime = syncManager.getTimeSinceLastSync(ClientSyncManager.EVENT_MONEY_UPDATED);
            source.sendFeedback(Text.literal("  Money Data: " + formatTime(moneySyncTime)).formatted(Formatting.WHITE));
            
            // Show pending syncs
            source.sendFeedback(Text.literal("Pending Syncs:").formatted(Formatting.YELLOW));
            source.sendFeedback(Text.literal("  Town: " + (syncManager.isSyncPending("town") ? "Yes" : "No")).formatted(Formatting.WHITE));
            source.sendFeedback(Text.literal("  Player: " + (syncManager.isSyncPending("player") ? "Yes" : "No")).formatted(Formatting.WHITE));
            source.sendFeedback(Text.literal("  Chunk: " + (syncManager.isSyncPending("chunk") ? "Yes" : "No")).formatted(Formatting.WHITE));
            source.sendFeedback(Text.literal("  Money: " + (syncManager.isSyncPending("money") ? "Yes" : "No")).formatted(Formatting.WHITE));
            
        } catch (Exception e) {
            source.sendError(Text.literal("Error checking client sync status: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in clientsync status command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Shows client-side synchronization statistics.
     */
    private static int showClientStats(CommandContext<FabricClientCommandSource> context) {
        FabricClientCommandSource source = context.getSource();
        
        try {
            ClientSyncManager syncManager = ClientSyncManager.getInstance();
            Map<String, Object> stats = syncManager.getSyncStats();
            
            source.sendFeedback(Text.literal("=== Client Synchronization Statistics ===").formatted(Formatting.GOLD));
            source.sendFeedback(Text.literal("Event Listeners: " + stats.get("eventListeners")).formatted(Formatting.YELLOW));
            source.sendFeedback(Text.literal("Last Sync Times: " + stats.get("lastSyncTimes")).formatted(Formatting.YELLOW));
            source.sendFeedback(Text.literal("Pending Syncs: " + stats.get("pendingSyncs")).formatted(Formatting.YELLOW));
            
            source.sendFeedback(Text.literal("Cache Statistics:").formatted(Formatting.YELLOW));
            source.sendFeedback(Text.literal("  Towns Cached: " + stats.get("townCacheStats")).formatted(Formatting.WHITE));
            source.sendFeedback(Text.literal("  Players Cached: " + stats.get("playerCacheStats")).formatted(Formatting.WHITE));
            
            @SuppressWarnings("unchecked")
            Map<String, Integer> chunkStats = (Map<String, Integer>) stats.get("chunkCacheStats");
            if (chunkStats != null) {
                source.sendFeedback(Text.literal("  Chunks Cached: " + chunkStats.get("cachedChunks")).formatted(Formatting.WHITE));
                source.sendFeedback(Text.literal("  Pending Chunk Updates: " + chunkStats.get("pendingUpdates")).formatted(Formatting.WHITE));
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Integer> electionStats = (Map<String, Integer>) stats.get("electionCacheStats");
            if (electionStats != null) {
                source.sendFeedback(Text.literal("  Elections Cached: " + electionStats.get("cachedElections")).formatted(Formatting.WHITE));
                source.sendFeedback(Text.literal("  Active Elections: " + electionStats.get("activeElections")).formatted(Formatting.WHITE));
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> moneyStats = (Map<String, Object>) stats.get("moneyCacheStats");
            if (moneyStats != null) {
                source.sendFeedback(Text.literal("  Money Balances Cached: " + moneyStats.get("cachedBalances")).formatted(Formatting.WHITE));
                source.sendFeedback(Text.literal("  Current Balance: " + moneyStats.get("currentPlayerBalance")).formatted(Formatting.WHITE));
                source.sendFeedback(Text.literal("  Watching Balance: " + moneyStats.get("isWatchingBalance")).formatted(Formatting.WHITE));
            }
            
        } catch (Exception e) {
            source.sendError(Text.literal("Error getting client sync stats: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in clientsync stats command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Clears client-side cache.
     */
    private static int clearClientCache(CommandContext<FabricClientCommandSource> context) {
        FabricClientCommandSource source = context.getSource();
        
        try {
            source.sendFeedback(Text.literal("Clearing client-side cache...").formatted(Formatting.YELLOW));
            
            // Clear all client-side managers
            ClientSyncManager.getInstance().clearAll();
            
            source.sendFeedback(Text.literal("Client cache cleared successfully!").formatted(Formatting.GREEN));
            
        } catch (Exception e) {
            source.sendError(Text.literal("Error clearing client cache: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in clientsync clear command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Runs client-side synchronization tests.
     */
    private static int runClientSyncTest(CommandContext<FabricClientCommandSource> context) {
        FabricClientCommandSource source = context.getSource();
        MinecraftClient client = source.getClient();
        
        try {
            if (client.player == null) {
                source.sendError(Text.literal("Player not available"));
                return 0;
            }
            
            source.sendFeedback(Text.literal("=== Running Client Synchronization Tests ===").formatted(Formatting.GOLD));
            
            int passedTests = 0;
            int totalTests = 0;
            
            // Test 1: Client Manager Initialization
            totalTests++;
            source.sendFeedback(Text.literal("Test 1: Client Manager Initialization").formatted(Formatting.YELLOW));
            try {
                ClientTownManager townManager = ClientTownManager.getInstance();
                ClientPlayerManager playerManager = ClientPlayerManager.getInstance();
                ClientChunkManager chunkManager = ClientChunkManager.getInstance();
                ClientElectionManager electionManager = ClientElectionManager.getInstance();
                ClientMoneyManager moneyManager = ClientMoneyManager.getInstance();
                ClientSyncManager syncManager = ClientSyncManager.getInstance();
                
                if (townManager != null && playerManager != null && chunkManager != null && 
                    electionManager != null && moneyManager != null && syncManager != null) {
                    source.sendFeedback(Text.literal("  ✓ All client managers initialized").formatted(Formatting.GREEN));
                    passedTests++;
                } else {
                    source.sendFeedback(Text.literal("  ✗ Some client managers not initialized").formatted(Formatting.RED));
                }
            } catch (Exception e) {
                source.sendFeedback(Text.literal("  ✗ Client manager initialization failed: " + e.getMessage()).formatted(Formatting.RED));
            }
            
            // Test 2: Event System
            totalTests++;
            source.sendFeedback(Text.literal("Test 2: Event System").formatted(Formatting.YELLOW));
            try {
                ClientSyncManager syncManager = ClientSyncManager.getInstance();
                
                // Test event listener registration
                boolean[] eventReceived = {false};
                syncManager.addEventListener("test_event", (data) -> {
                    eventReceived[0] = true;
                });
                
                // Fire test event
                syncManager.fireEvent("test_event", "test_data");
                
                if (eventReceived[0]) {
                    source.sendFeedback(Text.literal("  ✓ Event system working").formatted(Formatting.GREEN));
                    passedTests++;
                } else {
                    source.sendFeedback(Text.literal("  ✗ Event system not working").formatted(Formatting.RED));
                }
            } catch (Exception e) {
                source.sendFeedback(Text.literal("  ✗ Event system test failed: " + e.getMessage()).formatted(Formatting.RED));
            }
            
            // Test 3: Cache Operations
            totalTests++;
            source.sendFeedback(Text.literal("Test 3: Cache Operations").formatted(Formatting.YELLOW));
            try {
                ClientTownManager townManager = ClientTownManager.getInstance();
                
                // Test cache operations
                int initialSize = townManager.getAllTowns().size();
                townManager.clearCache();
                int clearedSize = townManager.getAllTowns().size();
                
                if (clearedSize == 0) {
                    source.sendFeedback(Text.literal("  ✓ Cache operations working").formatted(Formatting.GREEN));
                    passedTests++;
                } else {
                    source.sendFeedback(Text.literal("  ✗ Cache operations not working properly").formatted(Formatting.RED));
                }
            } catch (Exception e) {
                source.sendFeedback(Text.literal("  ✗ Cache operations test failed: " + e.getMessage()).formatted(Formatting.RED));
            }
            
            // Show results
            source.sendFeedback(Text.literal("=== Test Results ===").formatted(Formatting.GOLD));
            source.sendFeedback(Text.literal("Passed: " + passedTests + "/" + totalTests)
                .formatted(passedTests == totalTests ? Formatting.GREEN : Formatting.YELLOW));
            
            if (passedTests == totalTests) {
                source.sendFeedback(Text.literal("All client tests passed! Client synchronization is working correctly.").formatted(Formatting.GREEN));
            } else {
                source.sendFeedback(Text.literal("Some client tests failed. Check logs for details.").formatted(Formatting.RED));
            }
            
        } catch (Exception e) {
            source.sendError(Text.literal("Error running client sync tests: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in clientsync test command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Formats time duration for display.
     */
    private static String formatTime(long milliseconds) {
        if (milliseconds == Long.MAX_VALUE) {
            return "Never";
        }
        
        if (milliseconds < 1000) {
            return milliseconds + "ms ago";
        } else if (milliseconds < 60000) {
            return (milliseconds / 1000) + "s ago";
        } else if (milliseconds < 3600000) {
            return (milliseconds / 60000) + "m ago";
        } else {
            return (milliseconds / 3600000) + "h ago";
        }
    }
}
