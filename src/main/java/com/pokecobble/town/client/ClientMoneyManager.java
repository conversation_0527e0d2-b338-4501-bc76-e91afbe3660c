package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side money data manager.
 * Manages cached money data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientMoneyManager {
    private static ClientMoneyManager instance;
    
    // Cached money data
    private final Map<UUID, Double> playerBalances = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> balanceVersions = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Current player money cache
    private UUID currentPlayerId = null;
    private double currentPlayerBalance = 0.0;
    private int currentPlayerBalanceVersion = -1;
    
    // Transaction history cache
    private final Map<UUID, List<MoneyTransaction>> transactionHistory = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> transactionVersions = new ConcurrentHashMap<>();
    
    // Watching status
    private boolean isWatchingBalance = false;
    private long lastBalanceUpdate = 0;
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 60000; // 1 minute
    private static final int MAX_TRANSACTION_HISTORY = 100;
    
    private ClientMoneyManager() {}
    
    public static ClientMoneyManager getInstance() {
        if (instance == null) {
            instance = new ClientMoneyManager();
        }
        return instance;
    }
    
    /**
     * Updates a player's balance in the cache.
     */
    public void updatePlayerBalance(UUID playerId, double balance, int version) {
        if (playerId == null) {
            return;
        }
        
        int currentVersion = balanceVersions.getOrDefault(playerId, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            playerBalances.put(playerId, balance);
            balanceVersions.put(playerId, version);
            lastUpdateTimes.put(playerId, System.currentTimeMillis());
            
            // Update current player cache if this is the current player
            if (playerId.equals(currentPlayerId)) {
                currentPlayerBalance = balance;
                currentPlayerBalanceVersion = version;
                lastBalanceUpdate = System.currentTimeMillis();
            }
            
            Pokecobbleclaim.LOGGER.debug("Updated balance cache for player " + playerId + " (version " + version + ", balance: " + balance + ")");
        }
    }
    
    /**
     * Gets a player's balance from the cache.
     */
    public double getPlayerBalance(UUID playerId) {
        if (playerId == null) {
            return 0.0;
        }
        
        // Check if cache is expired
        if (isCacheExpired(playerId)) {
            invalidatePlayerBalance(playerId);
            return 0.0;
        }
        
        return playerBalances.getOrDefault(playerId, 0.0);
    }
    
    /**
     * Gets the version of a cached balance.
     */
    public int getBalanceVersion(UUID playerId) {
        return balanceVersions.getOrDefault(playerId, -1);
    }
    
    /**
     * Invalidates a player's balance from the cache.
     */
    public void invalidatePlayerBalance(UUID playerId) {
        playerBalances.remove(playerId);
        balanceVersions.remove(playerId);
        lastUpdateTimes.remove(playerId);
        
        // Clear current player cache if this is the current player
        if (playerId.equals(currentPlayerId)) {
            currentPlayerBalance = 0.0;
            currentPlayerBalanceVersion = -1;
        }
    }
    
    /**
     * Sets the current player.
     */
    public void setCurrentPlayer(UUID playerId, double balance, int version) {
        this.currentPlayerId = playerId;
        this.currentPlayerBalance = balance;
        this.currentPlayerBalanceVersion = version;
        this.lastBalanceUpdate = System.currentTimeMillis();
        
        // Also update in the general cache
        updatePlayerBalance(playerId, balance, version);
    }
    
    /**
     * Gets the current player's balance.
     */
    public double getCurrentPlayerBalance() {
        return currentPlayerBalance;
    }
    
    /**
     * Gets the current player's balance version.
     */
    public int getCurrentPlayerBalanceVersion() {
        return currentPlayerBalanceVersion;
    }
    
    /**
     * Sets whether the client is watching balance updates.
     */
    public void setWatchingBalance(boolean watching) {
        this.isWatchingBalance = watching;
        if (watching) {
            lastBalanceUpdate = System.currentTimeMillis();
        }
    }
    
    /**
     * Checks if the client is watching balance updates.
     */
    public boolean isWatchingBalance() {
        return isWatchingBalance;
    }
    
    /**
     * Gets the time since the last balance update.
     */
    public long getTimeSinceLastBalanceUpdate() {
        return System.currentTimeMillis() - lastBalanceUpdate;
    }
    
    /**
     * Adds a transaction to the history.
     */
    public void addTransaction(UUID playerId, MoneyTransaction transaction, int version) {
        if (playerId == null || transaction == null) {
            return;
        }
        
        int currentVersion = transactionVersions.getOrDefault(playerId, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            List<MoneyTransaction> history = transactionHistory.computeIfAbsent(playerId, k -> new ArrayList<>());
            
            // Add transaction and sort by timestamp (newest first)
            history.add(transaction);
            history.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
            
            // Limit history size
            if (history.size() > MAX_TRANSACTION_HISTORY) {
                history.subList(MAX_TRANSACTION_HISTORY, history.size()).clear();
            }
            
            transactionVersions.put(playerId, version);
            
            Pokecobbleclaim.LOGGER.debug("Added transaction to history for player " + playerId + " (version " + version + ")");
        }
    }
    
    /**
     * Gets the transaction history for a player.
     */
    public List<MoneyTransaction> getTransactionHistory(UUID playerId) {
        List<MoneyTransaction> history = transactionHistory.get(playerId);
        return history != null ? new ArrayList<>(history) : new ArrayList<>();
    }
    
    /**
     * Gets the transaction version for a player.
     */
    public int getTransactionVersion(UUID playerId) {
        return transactionVersions.getOrDefault(playerId, -1);
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        playerBalances.clear();
        balanceVersions.clear();
        lastUpdateTimes.clear();
        transactionHistory.clear();
        transactionVersions.clear();
        currentPlayerId = null;
        currentPlayerBalance = 0.0;
        currentPlayerBalanceVersion = -1;
        isWatchingBalance = false;
        lastBalanceUpdate = 0;
        
        Pokecobbleclaim.LOGGER.debug("Cleared money cache");
    }
    
    /**
     * Checks if a player's balance needs to be refreshed from the server.
     */
    public boolean needsRefresh(UUID playerId) {
        return !balanceVersions.containsKey(playerId) || isCacheExpired(playerId);
    }
    
    /**
     * Checks if the cache for a player's balance is expired.
     */
    private boolean isCacheExpired(UUID playerId) {
        Long lastUpdate = lastUpdateTimes.get(playerId);
        return lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS;
    }
    
    /**
     * Gets cache statistics.
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedBalances", playerBalances.size());
        stats.put("currentPlayerBalance", currentPlayerBalance);
        stats.put("isWatchingBalance", isWatchingBalance);
        stats.put("timeSinceLastUpdate", getTimeSinceLastBalanceUpdate());
        return stats;
    }
    
    /**
     * Represents a money transaction.
     */
    public static class MoneyTransaction {
        private final String type;
        private final double amount;
        private final String description;
        private final long timestamp;
        private final UUID fromPlayer;
        private final UUID toPlayer;
        
        public MoneyTransaction(String type, double amount, String description, long timestamp, UUID fromPlayer, UUID toPlayer) {
            this.type = type;
            this.amount = amount;
            this.description = description;
            this.timestamp = timestamp;
            this.fromPlayer = fromPlayer;
            this.toPlayer = toPlayer;
        }
        
        public String getType() { return type; }
        public double getAmount() { return amount; }
        public String getDescription() { return description; }
        public long getTimestamp() { return timestamp; }
        public UUID getFromPlayer() { return fromPlayer; }
        public UUID getToPlayer() { return toPlayer; }
    }
}
