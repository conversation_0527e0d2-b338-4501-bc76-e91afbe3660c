package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.election.Election;

import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side election data manager.
 * Man<PERSON> cached election data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientElectionManager {
    private static ClientElectionManager instance;
    
    // Cached election data
    private final Map<UUID, Election> elections = new ConcurrentHashMap<>(); // town ID -> election
    private final Map<UUID, Integer> electionVersions = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Vote tracking
    private final Map<UUID, UUID> playerVotes = new ConcurrentHashMap<>(); // town ID -> candidate ID
    private final Map<UUID, Integer> voteVersions = new ConcurrentHashMap<>();
    
    // Election results cache
    private final Map<UUID, UUID> electionWinners = new ConcurrentHashMap<>();
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 30000; // 30 seconds
    
    private ClientElectionManager() {}
    
    public static ClientElectionManager getInstance() {
        if (instance == null) {
            instance = new ClientElectionManager();
        }
        return instance;
    }
    
    /**
     * Updates an election in the cache.
     */
    public void updateElection(UUID townId, Election election, int version) {
        if (townId == null) {
            return;
        }
        
        int currentVersion = electionVersions.getOrDefault(townId, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            if (election != null) {
                elections.put(townId, election);
            } else {
                elections.remove(townId);
            }
            
            electionVersions.put(townId, version);
            lastUpdateTimes.put(townId, System.currentTimeMillis());
            
            Pokecobbleclaim.LOGGER.debug("Updated election cache for town " + townId + " (version " + version + ")");
        }
    }
    
    /**
     * Gets an election from the cache.
     */
    public Election getElection(UUID townId) {
        if (townId == null) {
            return null;
        }
        
        // Check if cache is expired
        if (isCacheExpired(townId)) {
            invalidateElection(townId);
            return null;
        }
        
        return elections.get(townId);
    }
    
    /**
     * Checks if a town has an active election.
     */
    public boolean hasActiveElection(UUID townId) {
        Election election = getElection(townId);
        return election != null && !election.isCompleted();
    }
    
    /**
     * Gets the version of a cached election.
     */
    public int getElectionVersion(UUID townId) {
        return electionVersions.getOrDefault(townId, -1);
    }
    
    /**
     * Invalidates an election from the cache.
     */
    public void invalidateElection(UUID townId) {
        elections.remove(townId);
        electionVersions.remove(townId);
        lastUpdateTimes.remove(townId);
        playerVotes.remove(townId);
        voteVersions.remove(townId);
        electionWinners.remove(townId);
    }
    
    /**
     * Updates a player's vote.
     */
    public void updatePlayerVote(UUID townId, UUID candidateId, int version) {
        if (townId == null) {
            return;
        }
        
        int currentVersion = voteVersions.getOrDefault(townId, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            if (candidateId != null) {
                playerVotes.put(townId, candidateId);
            } else {
                playerVotes.remove(townId);
            }
            
            voteVersions.put(townId, version);
            
            Pokecobbleclaim.LOGGER.debug("Updated vote cache for town " + townId + " (version " + version + ")");
        }
    }
    
    /**
     * Gets the player's vote for a town's election.
     */
    public UUID getPlayerVote(UUID townId) {
        return playerVotes.get(townId);
    }
    
    /**
     * Checks if the player has voted in a town's election.
     */
    public boolean hasPlayerVoted(UUID townId) {
        return playerVotes.containsKey(townId);
    }
    
    /**
     * Gets the vote version for a town.
     */
    public int getVoteVersion(UUID townId) {
        return voteVersions.getOrDefault(townId, -1);
    }
    
    /**
     * Updates the election winner.
     */
    public void updateElectionWinner(UUID townId, UUID winner) {
        if (townId != null) {
            if (winner != null) {
                electionWinners.put(townId, winner);
            } else {
                electionWinners.remove(townId);
            }
        }
    }

    /**
     * Gets the election winner for a town.
     */
    public UUID getElectionWinner(UUID townId) {
        return electionWinners.get(townId);
    }
    
    /**
     * Gets all candidates for a town's election.
     */
    public List<UUID> getElectionCandidates(UUID townId) {
        Election election = getElection(townId);
        return election != null ? election.getCandidates() : new ArrayList<>();
    }

    /**
     * Gets the candidate with the most votes.
     */
    public UUID getLeadingCandidate(UUID townId) {
        Election election = getElection(townId);
        if (election == null) {
            return null;
        }

        List<UUID> candidates = election.getCandidates();
        if (candidates.isEmpty()) {
            return null;
        }

        // Find candidate with most votes
        UUID leading = candidates.get(0);
        Map<UUID, Integer> voteCount = election.getVoteCount();
        for (UUID candidate : candidates) {
            if (voteCount.getOrDefault(candidate, 0) > voteCount.getOrDefault(leading, 0)) {
                leading = candidate;
            }
        }

        return leading;
    }
    
    /**
     * Gets the total number of votes cast in an election.
     */
    public int getTotalVotes(UUID townId) {
        Election election = getElection(townId);
        if (election == null) {
            return 0;
        }

        int total = 0;
        Map<UUID, Integer> voteCount = election.getVoteCount();
        for (UUID candidate : election.getCandidates()) {
            total += voteCount.getOrDefault(candidate, 0);
        }

        return total;
    }
    
    /**
     * Checks if an election is ending soon.
     */
    public boolean isElectionEndingSoon(UUID townId) {
        Election election = getElection(townId);
        if (election == null || election.isCompleted()) {
            return false;
        }

        long timeLeft = election.getEndTime() - System.currentTimeMillis();
        return timeLeft > 0 && timeLeft < 300000; // 5 minutes
    }

    /**
     * Gets the time remaining in an election.
     */
    public long getElectionTimeRemaining(UUID townId) {
        Election election = getElection(townId);
        if (election == null || election.isCompleted()) {
            return 0;
        }

        return Math.max(0, election.getEndTime() - System.currentTimeMillis());
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        elections.clear();
        electionVersions.clear();
        lastUpdateTimes.clear();
        playerVotes.clear();
        voteVersions.clear();
        electionWinners.clear();
        
        Pokecobbleclaim.LOGGER.debug("Cleared election cache");
    }
    
    /**
     * Checks if an election needs to be refreshed from the server.
     */
    public boolean needsRefresh(UUID townId) {
        return !electionVersions.containsKey(townId) || isCacheExpired(townId);
    }
    
    /**
     * Checks if the cache for an election is expired.
     */
    private boolean isCacheExpired(UUID townId) {
        Long lastUpdate = lastUpdateTimes.get(townId);
        return lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS;
    }
    
    /**
     * Gets all towns with active elections.
     */
    public Set<UUID> getTownsWithActiveElections() {
        Set<UUID> activeTowns = new HashSet<>();
        for (Map.Entry<UUID, Election> entry : elections.entrySet()) {
            if (!entry.getValue().isCompleted() && !isCacheExpired(entry.getKey())) {
                activeTowns.add(entry.getKey());
            }
        }
        return activeTowns;
    }
    
    /**
     * Gets cache statistics.
     */
    public Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("cachedElections", elections.size());
        stats.put("playerVotes", playerVotes.size());
        stats.put("activeElections", getTownsWithActiveElections().size());
        return stats;
    }
}
