package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.claim.ClaimTag;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.util.math.ChunkPos;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side chunk data manager.
 * Manages cached chunk claim data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientChunkManager {
    private static ClientChunkManager instance;
    
    // Cached chunk data
    private final Map<ChunkPos, UUID> chunkOwners = new ConcurrentHashMap<>(); // chunk -> town ID
    private final Map<ChunkPos, ClaimTag> chunkTags = new ConcurrentHashMap<>(); // chunk -> tag
    private final Map<ChunkPos, Integer> chunkVersions = new ConcurrentHashMap<>();
    private final Map<ChunkPos, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Batch update tracking
    private final Set<ChunkPos> pendingUpdates = ConcurrentHashMap.newKeySet();
    private int globalChunkVersion = -1;
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 60000; // 1 minute
    private static final int MAX_CACHED_CHUNKS = 10000; // Limit memory usage
    
    private ClientChunkManager() {}
    
    public static ClientChunkManager getInstance() {
        if (instance == null) {
            instance = new ClientChunkManager();
        }
        return instance;
    }
    
    /**
     * Updates a chunk in the cache.
     */
    public void updateChunk(ChunkPos chunkPos, UUID townId, ClaimTag tag, int version) {
        if (chunkPos == null) {
            return;
        }
        
        int currentVersion = chunkVersions.getOrDefault(chunkPos, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            if (townId != null) {
                chunkOwners.put(chunkPos, townId);
            } else {
                chunkOwners.remove(chunkPos);
            }
            
            if (tag != null) {
                chunkTags.put(chunkPos, tag);
            } else {
                chunkTags.remove(chunkPos);
            }
            
            chunkVersions.put(chunkPos, version);
            lastUpdateTimes.put(chunkPos, System.currentTimeMillis());
            
            // Remove from pending updates
            pendingUpdates.remove(chunkPos);
            
            // Cleanup old chunks if we have too many
            cleanupOldChunks();
            
            Pokecobbleclaim.LOGGER.debug("Updated chunk cache for " + chunkPos + " (version " + version + ")");
        }
    }
    
    /**
     * Gets the town that owns a chunk.
     */
    public UUID getChunkOwner(ChunkPos chunkPos) {
        if (chunkPos == null) {
            return null;
        }
        
        // Check if cache is expired
        if (isCacheExpired(chunkPos)) {
            invalidateChunk(chunkPos);
            return null;
        }
        
        return chunkOwners.get(chunkPos);
    }
    
    /**
     * Gets the tag for a chunk.
     */
    public ClaimTag getChunkTag(ChunkPos chunkPos) {
        if (chunkPos == null) {
            return null;
        }
        
        // Check if cache is expired
        if (isCacheExpired(chunkPos)) {
            invalidateChunk(chunkPos);
            return null;
        }
        
        return chunkTags.get(chunkPos);
    }
    
    /**
     * Checks if a chunk is claimed.
     */
    public boolean isChunkClaimed(ChunkPos chunkPos) {
        return getChunkOwner(chunkPos) != null;
    }
    
    /**
     * Gets the version of a cached chunk.
     */
    public int getChunkVersion(ChunkPos chunkPos) {
        return chunkVersions.getOrDefault(chunkPos, -1);
    }
    
    /**
     * Invalidates a chunk from the cache.
     */
    public void invalidateChunk(ChunkPos chunkPos) {
        chunkOwners.remove(chunkPos);
        chunkTags.remove(chunkPos);
        chunkVersions.remove(chunkPos);
        lastUpdateTimes.remove(chunkPos);
        pendingUpdates.remove(chunkPos);
    }
    
    /**
     * Marks a chunk as needing an update.
     */
    public void markChunkForUpdate(ChunkPos chunkPos) {
        if (chunkPos != null) {
            pendingUpdates.add(chunkPos);
        }
    }
    
    /**
     * Gets all chunks that need updates.
     */
    public Set<ChunkPos> getPendingUpdates() {
        return new HashSet<>(pendingUpdates);
    }
    
    /**
     * Clears pending updates.
     */
    public void clearPendingUpdates() {
        pendingUpdates.clear();
    }
    
    /**
     * Updates the global chunk version.
     */
    public void updateGlobalVersion(int version) {
        if (version > globalChunkVersion) {
            globalChunkVersion = version;
        }
    }
    
    /**
     * Gets the global chunk version.
     */
    public int getGlobalVersion() {
        return globalChunkVersion;
    }
    
    /**
     * Gets all chunks owned by a town.
     */
    public Set<ChunkPos> getChunksOwnedByTown(UUID townId) {
        if (townId == null) {
            return new HashSet<>();
        }
        
        Set<ChunkPos> ownedChunks = new HashSet<>();
        for (Map.Entry<ChunkPos, UUID> entry : chunkOwners.entrySet()) {
            if (townId.equals(entry.getValue())) {
                // Check if cache is still valid
                if (!isCacheExpired(entry.getKey())) {
                    ownedChunks.add(entry.getKey());
                }
            }
        }
        
        return ownedChunks;
    }
    
    /**
     * Gets all chunks with a specific tag.
     */
    public Set<ChunkPos> getChunksWithTag(ClaimTag tag) {
        if (tag == null) {
            return new HashSet<>();
        }
        
        Set<ChunkPos> taggedChunks = new HashSet<>();
        for (Map.Entry<ChunkPos, ClaimTag> entry : chunkTags.entrySet()) {
            if (tag.equals(entry.getValue())) {
                // Check if cache is still valid
                if (!isCacheExpired(entry.getKey())) {
                    taggedChunks.add(entry.getKey());
                }
            }
        }
        
        return taggedChunks;
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        chunkOwners.clear();
        chunkTags.clear();
        chunkVersions.clear();
        lastUpdateTimes.clear();
        pendingUpdates.clear();
        globalChunkVersion = -1;
        
        Pokecobbleclaim.LOGGER.debug("Cleared chunk cache");
    }
    
    /**
     * Checks if a chunk needs to be refreshed from the server.
     */
    public boolean needsRefresh(ChunkPos chunkPos) {
        return !chunkVersions.containsKey(chunkPos) || isCacheExpired(chunkPos);
    }
    
    /**
     * Checks if the cache for a chunk is expired.
     */
    private boolean isCacheExpired(ChunkPos chunkPos) {
        Long lastUpdate = lastUpdateTimes.get(chunkPos);
        return lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS;
    }
    
    /**
     * Cleans up old chunks to prevent memory issues.
     */
    private void cleanupOldChunks() {
        if (chunkOwners.size() <= MAX_CACHED_CHUNKS) {
            return;
        }
        
        // Find the oldest chunks and remove them
        List<Map.Entry<ChunkPos, Long>> entries = new ArrayList<>();
        for (Map.Entry<ChunkPos, Long> entry : lastUpdateTimes.entrySet()) {
            entries.add(entry);
        }
        
        // Sort by update time (oldest first)
        entries.sort(Map.Entry.comparingByValue());
        
        // Remove the oldest 10% of chunks
        int toRemove = Math.max(1, chunkOwners.size() - MAX_CACHED_CHUNKS + (MAX_CACHED_CHUNKS / 10));
        for (int i = 0; i < toRemove && i < entries.size(); i++) {
            ChunkPos chunkPos = entries.get(i).getKey();
            invalidateChunk(chunkPos);
        }
        
        Pokecobbleclaim.LOGGER.debug("Cleaned up " + toRemove + " old chunks from cache");
    }
    
    /**
     * Gets cache statistics.
     */
    public Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("cachedChunks", chunkOwners.size());
        stats.put("pendingUpdates", pendingUpdates.size());
        stats.put("globalVersion", globalChunkVersion);
        return stats;
    }

    /**
     * Updates chunk permissions (tag) in the cache.
     */
    public void updateChunkPermissions(ChunkPos chunkPos, ClaimTag tag, int version) {
        if (chunkPos == null) {
            return;
        }

        int currentVersion = chunkVersions.getOrDefault(chunkPos, -1);

        // Only update if version is newer
        if (version > currentVersion) {
            if (tag != null) {
                chunkTags.put(chunkPos, tag);
            } else {
                chunkTags.remove(chunkPos);
            }

            chunkVersions.put(chunkPos, version);
            lastUpdateTimes.put(chunkPos, System.currentTimeMillis());

            // Remove from pending updates
            pendingUpdates.remove(chunkPos);

            Pokecobbleclaim.LOGGER.debug("Updated chunk permissions for " + chunkPos + " (version " + version + ")");
        }
    }
}
