package com.pokecobble.town.client;

import com.pokecobble.town.Town;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.ChunkPermissionManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;

/**
 * Tracks the player's current chunk and detects when they enter a claimed chunk.
 * Optimized to reduce unnecessary calculations and improve performance.
 */
public class ChunkTracker {
    private static ChunkPos lastChunk = null;
    private static BlockPos lastPlayerPos = null;
    private static long lastUpdateTime = 0;

    // Get update interval from config
    private static long getUpdateInterval() {
        return com.pokecobble.config.PerformanceConfig.getChunkTrackerUpdateInterval();
    }

    /**
     * Updates the chunk tracker.
     * Optimized to reduce unnecessary calculations.
     */
    public static void update() {
        MinecraftClient client = MinecraftClient.getInstance();
        ClientPlayerEntity player = client.player;

        // Skip if player is null or game is paused
        if (player == null || client.isPaused()) {
            return;
        }

        // Get current time
        long currentTime = System.currentTimeMillis();

        // Only check for chunk changes periodically to reduce CPU usage
        if (currentTime - lastUpdateTime < getUpdateInterval()) {
            return;
        }

        // Update the last update time
        lastUpdateTime = currentTime;

        // Get current player position
        BlockPos playerPos = player.getBlockPos();

        // Skip if player hasn't moved
        if (lastPlayerPos != null && playerPos.equals(lastPlayerPos)) {
            return;
        }

        // Update last player position
        lastPlayerPos = playerPos;

        // Get current chunk
        ChunkPos currentChunk = new ChunkPos(playerPos);

        // Check if player has moved to a new chunk
        if (lastChunk == null || !currentChunk.equals(lastChunk)) {
            // Player has entered a new chunk
            onChunkEnter(currentChunk);
            lastChunk = currentChunk;
        }

        // Update notification
        ClaimedChunkNotification.update();

        // Update claim tool data synchronizer if active
        if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
            ClaimToolDataSynchronizer.getInstance().updateIfNeeded();
        }
    }

    /**
     * Sets the current chunk.
     * This is used by the ClaimTool to update the chunk tracker.
     *
     * @param chunk The current chunk position
     */
    public static void setCurrentChunk(ChunkPos chunk) {
        // Only update if the chunk has changed
        if (lastChunk == null || !chunk.equals(lastChunk)) {
            // Update the last chunk
            lastChunk = chunk;

            // Call the chunk enter handler
            onChunkEnter(chunk);
        }
    }

    /**
     * Called when the player enters a new chunk.
     *
     * @param chunk The chunk position
     */
    private static void onChunkEnter(ChunkPos chunk) {
        // Check if the chunk is claimed
        Town town = ChunkPermissionManager.getInstance().getTownForChunk(chunk);

        if (town != null) {
            // Chunk is claimed, get the tag
            ClaimTag tag = ChunkPermissionManager.getInstance().getTagForChunk(chunk);

            // Show notification
            ClaimedChunkNotification.show(town.getName(), tag != null ? tag.getName() : "unknown", tag != null ? tag.getColor() : 0xFFFFFF);
        } else {
            // Chunk is not claimed, clear notification
            ClaimedChunkNotification.hide();
        }
    }
}
