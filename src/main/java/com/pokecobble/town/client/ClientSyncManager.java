package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * Client-side synchronization manager that handles data updates and notifies listeners.
 */
@Environment(EnvType.CLIENT)
public class ClientSyncManager {
    private static ClientSyncManager instance;
    
    // Event listeners
    private final Map<String, Set<Consumer<Object>>> eventListeners = new ConcurrentHashMap<>();
    
    // Sync status tracking
    private final Map<String, Long> lastSyncTimes = new ConcurrentHashMap<>();
    private final Set<String> pendingSyncs = ConcurrentHashMap.newKeySet();
    
    // Event types
    public static final String EVENT_TOWN_UPDATED = "town_updated";
    public static final String EVENT_PLAYER_UPDATED = "player_updated";
    public static final String EVENT_CHUNK_UPDATED = "chunk_updated";
    public static final String EVENT_ELECTION_UPDATED = "election_updated";
    public static final String EVENT_MONEY_UPDATED = "money_updated";
    public static final String EVENT_SYNC_COMPLETE = "sync_complete";
    public static final String EVENT_SYNC_FAILED = "sync_failed";
    
    private ClientSyncManager() {}
    
    public static ClientSyncManager getInstance() {
        if (instance == null) {
            instance = new ClientSyncManager();
        }
        return instance;
    }
    
    /**
     * Registers an event listener.
     */
    public void addEventListener(String eventType, Consumer<Object> listener) {
        eventListeners.computeIfAbsent(eventType, k -> ConcurrentHashMap.newKeySet()).add(listener);
    }

    /**
     * Adds an event handler (alias for addEventListener for compatibility).
     */
    public void addEventHandler(String eventType, Consumer<Object> handler) {
        addEventListener(eventType, handler);
    }
    
    /**
     * Removes an event listener.
     */
    public void removeEventListener(String eventType, Consumer<Object> listener) {
        Set<Consumer<Object>> listeners = eventListeners.get(eventType);
        if (listeners != null) {
            listeners.remove(listener);
            if (listeners.isEmpty()) {
                eventListeners.remove(eventType);
            }
        }
    }
    
    /**
     * Fires an event to all registered listeners.
     */
    public void fireEvent(String eventType, Object data) {
        Set<Consumer<Object>> listeners = eventListeners.get(eventType);
        if (listeners != null) {
            for (Consumer<Object> listener : listeners) {
                try {
                    listener.accept(data);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in event listener for " + eventType + ": " + e.getMessage());
                }
            }
        }
        
        // Update sync time
        lastSyncTimes.put(eventType, System.currentTimeMillis());
        pendingSyncs.remove(eventType);
        
        Pokecobbleclaim.LOGGER.debug("Fired event: " + eventType);
    }
    
    /**
     * Marks a sync as pending.
     */
    public void markSyncPending(String syncType) {
        pendingSyncs.add(syncType);
    }
    
    /**
     * Checks if a sync is pending.
     */
    public boolean isSyncPending(String syncType) {
        return pendingSyncs.contains(syncType);
    }
    
    /**
     * Gets the time since last sync for a specific type.
     */
    public long getTimeSinceLastSync(String syncType) {
        Long lastSync = lastSyncTimes.get(syncType);
        return lastSync != null ? System.currentTimeMillis() - lastSync : Long.MAX_VALUE;
    }
    
    /**
     * Handles town data updates.
     */
    public void onTownUpdated(UUID townId, int version) {
        Map<String, Object> data = new HashMap<>();
        data.put("townId", townId);
        data.put("version", version);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_TOWN_UPDATED, data);
        
        // Update UI components that depend on town data
        updateTownDependentComponents(townId);
    }
    
    /**
     * Handles player data updates.
     */
    public void onPlayerUpdated(UUID playerId, int version) {
        Map<String, Object> data = new HashMap<>();
        data.put("playerId", playerId);
        data.put("version", version);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_PLAYER_UPDATED, data);
        
        // Update UI components that depend on player data
        updatePlayerDependentComponents(playerId);
    }
    
    /**
     * Handles chunk data updates.
     */
    public void onChunkUpdated(net.minecraft.util.math.ChunkPos chunkPos, int version) {
        Map<String, Object> data = new HashMap<>();
        data.put("chunkPos", chunkPos);
        data.put("version", version);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_CHUNK_UPDATED, data);
        
        // Update claim tool if active
        updateClaimToolIfActive(chunkPos);
    }
    
    /**
     * Handles election data updates.
     */
    public void onElectionUpdated(UUID townId, int version) {
        Map<String, Object> data = new HashMap<>();
        data.put("townId", townId);
        data.put("version", version);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_ELECTION_UPDATED, data);
    }
    
    /**
     * Handles money data updates.
     */
    public void onMoneyUpdated(UUID playerId, double balance, int version) {
        Map<String, Object> data = new HashMap<>();
        data.put("playerId", playerId);
        data.put("balance", balance);
        data.put("version", version);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_MONEY_UPDATED, data);
        
        // Update phone apps that display money
        updateMoneyDependentComponents(playerId, balance);
    }
    
    /**
     * Handles sync completion.
     */
    public void onSyncComplete(String syncType) {
        Map<String, Object> data = new HashMap<>();
        data.put("syncType", syncType);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_SYNC_COMPLETE, data);
    }
    
    /**
     * Handles sync failure.
     */
    public void onSyncFailed(String syncType, String error) {
        Map<String, Object> data = new HashMap<>();
        data.put("syncType", syncType);
        data.put("error", error);
        data.put("timestamp", System.currentTimeMillis());
        
        fireEvent(EVENT_SYNC_FAILED, data);
    }
    
    /**
     * Updates UI components that depend on town data.
     */
    private void updateTownDependentComponents(UUID townId) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            return;
        }
        
        // Browser UI updates removed
        
        // Update claim tool HUD
        try {
            com.pokecobble.town.client.ModernClaimToolHud.getInstance().refresh();
        } catch (Exception e) {
            // Claim tool not active, ignore
        }
    }
    
    /**
     * Updates UI components that depend on player data.
     */
    private void updatePlayerDependentComponents(UUID playerId) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || !playerId.equals(client.player.getUuid())) {
            return;
        }
        
        // Update phone UI
        try {
            com.pokecobble.phone.PhoneManager.getInstance().refreshPlayerData();
        } catch (Exception e) {
            // Phone not available, ignore
        }
    }
    
    /**
     * Updates the claim tool if it's active.
     */
    private void updateClaimToolIfActive(net.minecraft.util.math.ChunkPos chunkPos) {
        try {
            com.pokecobble.town.claim.ClaimTool claimTool = com.pokecobble.town.claim.ClaimTool.getInstance();
            if (claimTool.isActive()) {
                // Refresh chunk data in claim tool
                claimTool.refreshChunkData(chunkPos);
            }
        } catch (Exception e) {
            // Claim tool not available, ignore
        }
    }
    
    /**
     * Updates UI components that depend on money data.
     */
    private void updateMoneyDependentComponents(UUID playerId, double balance) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || !playerId.equals(client.player.getUuid())) {
            return;
        }
        
        // Update phone bank app if open
        try {
            // This would update the bank app in the phone system
            // Implementation depends on the phone app architecture
        } catch (Exception e) {
            // Phone not available, ignore
        }
        
        // Browser UI updates removed
    }
    
    /**
     * Clears all cached data and event listeners.
     */
    public void clearAll() {
        eventListeners.clear();
        lastSyncTimes.clear();
        pendingSyncs.clear();
        
        // Clear all client-side managers
        ClientTownManager.getInstance().clearCache();
        ClientPlayerManager.getInstance().clearCache();
        ClientChunkManager.getInstance().clearCache();
        ClientElectionManager.getInstance().clearCache();
        ClientMoneyManager.getInstance().clearCache();
        
        Pokecobbleclaim.LOGGER.debug("Cleared all client sync data");
    }
    
    /**
     * Gets synchronization statistics.
     */
    public Map<String, Object> getSyncStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("eventListeners", eventListeners.size());
        stats.put("lastSyncTimes", lastSyncTimes.size());
        stats.put("pendingSyncs", pendingSyncs.size());
        
        // Add manager stats
        stats.put("townCacheStats", ClientTownManager.getInstance().getAllTowns().size());
        stats.put("playerCacheStats", ClientPlayerManager.getInstance().getAllPlayers().size());
        stats.put("chunkCacheStats", ClientChunkManager.getInstance().getCacheStats());
        stats.put("electionCacheStats", ClientElectionManager.getInstance().getCacheStats());
        stats.put("moneyCacheStats", ClientMoneyManager.getInstance().getCacheStats());
        
        return stats;
    }
}
