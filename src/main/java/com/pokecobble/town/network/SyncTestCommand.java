package com.pokecobble.town.network;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import com.pokecobble.town.network.player.PlayerDataSynchronizer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.math.ChunkPos;

import java.util.Map;
import java.util.UUID;

/**
 * Command for testing and validating synchronization functionality.
 */
public class SyncTestCommand {
    
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess) {
        dispatcher.register(CommandManager.literal("synctest")
            .requires(source -> source.hasPermissionLevel(2))
            .then(CommandManager.literal("status")
                .executes(SyncTestCommand::showSyncStatus))
            .then(CommandManager.literal("force")
                .executes(SyncTestCommand::forceSync))
            .then(CommandManager.literal("test")
                .executes(SyncTestCommand::runSyncTest))
            .then(CommandManager.literal("stats")
                .executes(SyncTestCommand::showStats))
            .then(CommandManager.literal("clear")
                .executes(SyncTestCommand::clearCache))
        );
    }
    
    /**
     * Shows synchronization status for the player.
     */
    private static int showSyncStatus(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            if (source.getEntity() instanceof ServerPlayerEntity player) {
                UUID playerId = player.getUuid();
                
                // Check server-side sync status
                SynchronizationManager syncManager = SynchronizationManager.getInstance();
                boolean isFullySynced = syncManager.isPlayerFullySynced(playerId);
                
                source.sendFeedback(() -> Text.literal("=== Synchronization Status ===").formatted(Formatting.GOLD), false);
                source.sendFeedback(() -> Text.literal("Player: " + player.getName().getString()).formatted(Formatting.YELLOW), false);
                source.sendFeedback(() -> Text.literal("Fully Synced: " + (isFullySynced ? "✓" : "✗"))
                    .formatted(isFullySynced ? Formatting.GREEN : Formatting.RED), false);
                
                // Show individual sync status
                var syncStatus = syncManager.getPlayerSyncStatus(playerId);
                source.sendFeedback(() -> Text.literal("Sync Status:").formatted(Formatting.YELLOW), false);
                source.sendFeedback(() -> Text.literal("  Town Data: " + (syncStatus.contains(SynchronizationManager.SYNC_TOWN_DATA) ? "✓" : "✗"))
                    .formatted(syncStatus.contains(SynchronizationManager.SYNC_TOWN_DATA) ? Formatting.GREEN : Formatting.RED), false);
                source.sendFeedback(() -> Text.literal("  Player Data: " + (syncStatus.contains(SynchronizationManager.SYNC_PLAYER_DATA) ? "✓" : "✗"))
                    .formatted(syncStatus.contains(SynchronizationManager.SYNC_PLAYER_DATA) ? Formatting.GREEN : Formatting.RED), false);
                source.sendFeedback(() -> Text.literal("  Chunk Data: " + (syncStatus.contains(SynchronizationManager.SYNC_CHUNK_DATA) ? "✓" : "✗"))
                    .formatted(syncStatus.contains(SynchronizationManager.SYNC_CHUNK_DATA) ? Formatting.GREEN : Formatting.RED), false);
                source.sendFeedback(() -> Text.literal("  Money Data: " + (syncStatus.contains(SynchronizationManager.SYNC_MONEY_DATA) ? "✓" : "✗"))
                    .formatted(syncStatus.contains(SynchronizationManager.SYNC_MONEY_DATA) ? Formatting.GREEN : Formatting.RED), false);
                
            } else {
                source.sendError(Text.literal("This command can only be used by players"));
                return 0;
            }
        } catch (Exception e) {
            source.sendError(Text.literal("Error checking sync status: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in synctest status command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Forces a full resynchronization for the player.
     */
    private static int forceSync(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            if (source.getEntity() instanceof ServerPlayerEntity player) {
                source.sendFeedback(() -> Text.literal("Forcing full resynchronization...").formatted(Formatting.YELLOW), false);
                
                SynchronizationManager syncManager = SynchronizationManager.getInstance();
                syncManager.forceResync(source.getServer(), player);
                
                source.sendFeedback(() -> Text.literal("Resynchronization completed!").formatted(Formatting.GREEN), false);
            } else {
                source.sendError(Text.literal("This command can only be used by players"));
                return 0;
            }
        } catch (Exception e) {
            source.sendError(Text.literal("Error forcing sync: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in synctest force command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Runs comprehensive synchronization tests.
     */
    private static int runSyncTest(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            if (source.getEntity() instanceof ServerPlayerEntity player) {
                source.sendFeedback(() -> Text.literal("=== Running Synchronization Tests ===").formatted(Formatting.GOLD), false);
                
                // Use arrays to make variables effectively final for lambda expressions
                final int[] testCounts = {0, 0}; // [passedTests, totalTests]

                // Test 1: Town Data Sync
                testCounts[1]++;
                source.sendFeedback(() -> Text.literal("Test 1: Town Data Synchronization").formatted(Formatting.YELLOW), false);
                try {
                    TownDataSynchronizer.syncPlayerTownData(source.getServer(), player.getUuid());
                    source.sendFeedback(() -> Text.literal("  ✓ Town data sync completed").formatted(Formatting.GREEN), false);
                    testCounts[0]++;
                } catch (Exception e) {
                    source.sendFeedback(() -> Text.literal("  ✗ Town data sync failed: " + e.getMessage()).formatted(Formatting.RED), false);
                }

                // Test 2: Player Data Sync
                testCounts[1]++;
                source.sendFeedback(() -> Text.literal("Test 2: Player Data Synchronization").formatted(Formatting.YELLOW), false);
                try {
                    PlayerDataSynchronizer.syncPlayerData(source.getServer(), player.getUuid());
                    source.sendFeedback(() -> Text.literal("  ✓ Player data sync completed").formatted(Formatting.GREEN), false);
                    testCounts[0]++;
                } catch (Exception e) {
                    source.sendFeedback(() -> Text.literal("  ✗ Player data sync failed: " + e.getMessage()).formatted(Formatting.RED), false);
                }

                // Test 3: Chunk Data Sync
                testCounts[1]++;
                source.sendFeedback(() -> Text.literal("Test 3: Chunk Data Synchronization").formatted(Formatting.YELLOW), false);
                try {
                    ChunkDataSynchronizer.syncChunkData(source.getServer());
                    source.sendFeedback(() -> Text.literal("  ✓ Chunk data sync completed").formatted(Formatting.GREEN), false);
                    testCounts[0]++;
                } catch (Exception e) {
                    source.sendFeedback(() -> Text.literal("  ✗ Chunk data sync failed: " + e.getMessage()).formatted(Formatting.RED), false);
                }

                // Test 4: Create test town and sync
                testCounts[1]++;
                source.sendFeedback(() -> Text.literal("Test 4: Test Town Creation and Sync").formatted(Formatting.YELLOW), false);
                try {
                    Town testTown = new Town("TestTown_" + System.currentTimeMillis(), "Test town for sync validation", true, 10);
                    testTown.addPlayer(player.getUuid());
                    TownManager.getInstance().addTown(testTown);

                    // Sync the new town
                    TownDataSynchronizer.syncPlayerTownData(source.getServer(), player.getUuid());

                    source.sendFeedback(() -> Text.literal("  ✓ Test town created and synced").formatted(Formatting.GREEN), false);
                    testCounts[0]++;
                } catch (Exception e) {
                    source.sendFeedback(() -> Text.literal("  ✗ Test town creation failed: " + e.getMessage()).formatted(Formatting.RED), false);
                }

                // Show results
                source.sendFeedback(() -> Text.literal("=== Test Results ===").formatted(Formatting.GOLD), false);
                source.sendFeedback(() -> Text.literal("Passed: " + testCounts[0] + "/" + testCounts[1])
                    .formatted(testCounts[0] == testCounts[1] ? Formatting.GREEN : Formatting.YELLOW), false);

                if (testCounts[0] == testCounts[1]) {
                    source.sendFeedback(() -> Text.literal("All tests passed! Synchronization is working correctly.").formatted(Formatting.GREEN), false);
                } else {
                    source.sendFeedback(() -> Text.literal("Some tests failed. Check logs for details.").formatted(Formatting.RED), false);
                }
                
            } else {
                source.sendError(Text.literal("This command can only be used by players"));
                return 0;
            }
        } catch (Exception e) {
            source.sendError(Text.literal("Error running sync tests: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in synctest test command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Shows synchronization statistics.
     */
    private static int showStats(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            SynchronizationManager syncManager = SynchronizationManager.getInstance();
            Map<String, Object> stats = syncManager.getSyncStats();
            
            source.sendFeedback(() -> Text.literal("=== Synchronization Statistics ===").formatted(Formatting.GOLD), false);
            source.sendFeedback(() -> Text.literal("Total Players: " + stats.get("totalPlayers")).formatted(Formatting.YELLOW), false);
            source.sendFeedback(() -> Text.literal("Active Retries: " + stats.get("activeRetries")).formatted(Formatting.YELLOW), false);
            source.sendFeedback(() -> Text.literal("Last Sync Operations: " + stats.get("lastSyncOperations")).formatted(Formatting.YELLOW), false);
            source.sendFeedback(() -> Text.literal("Fully Synced Players: " + stats.get("fullySyncedPlayers")).formatted(Formatting.YELLOW), false);
            
        } catch (Exception e) {
            source.sendError(Text.literal("Error getting sync stats: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in synctest stats command", e);
            return 0;
        }
        
        return 1;
    }
    
    /**
     * Clears client-side cache (client-only command).
     */
    private static int clearCache(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            if (source.getEntity() instanceof ServerPlayerEntity player) {
                source.sendFeedback(() -> Text.literal("Cache clearing is handled client-side. Use F3+A to reload chunks or restart client.").formatted(Formatting.YELLOW), false);
            } else {
                source.sendError(Text.literal("This command can only be used by players"));
                return 0;
            }
        } catch (Exception e) {
            source.sendError(Text.literal("Error clearing cache: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in synctest clear command", e);
            return 0;
        }
        
        return 1;
    }
}
