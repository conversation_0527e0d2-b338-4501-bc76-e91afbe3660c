package com.pokecobble.town.network.chunk;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.claim.ChunkPermissionManager;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.ChunkPos;

import java.util.*;

/**
 * Handles synchronization of chunk claim data between server and clients.
 * This class is responsible for efficiently sending chunk updates to the appropriate players.
 */
public class ChunkDataSynchronizer {
    // Constants for chunk update packets
    public static final Identifier CHUNK_UPDATE = new Identifier(NetworkConstants.MOD_ID, "chunk_update");
    public static final Identifier CHUNK_BATCH_UPDATE = new Identifier(NetworkConstants.MOD_ID, "chunk_batch_update");
    
    // Map to track the last known data version for each player's chunk view
    private static final Map<UUID, Integer> playerChunkVersions = new HashMap<>();
    
    // Current chunk data version (incremented on each change)
    private static int currentChunkVersion = 0;
    
    // Set of recently changed chunks
    private static final Set<ChunkPos> changedChunks = new HashSet<>();
    
    /**
     * Registers client-side packet handlers for chunk data synchronization.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register chunk update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                CHUNK_UPDATE,
                ChunkDataSynchronizer::handleChunkUpdate
        );
        
        // Register chunk batch update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                CHUNK_BATCH_UPDATE,
                ChunkDataSynchronizer::handleChunkBatchUpdate
        );
    }
    
    /**
     * Registers server-side packet handlers for chunk data synchronization.
     */
    public static void registerServerHandlers() {
        // No server-side handlers needed for these packets
        // These are server->client only
    }
    
    /**
     * Marks a chunk as changed and increments the chunk data version.
     * This should be called whenever a chunk's claim status or tag changes.
     *
     * @param chunkPos The chunk position that changed
     */
    public static void markChunkChanged(ChunkPos chunkPos) {
        changedChunks.add(chunkPos);
        currentChunkVersion++;
    }
    
    /**
     * Marks multiple chunks as changed and increments the chunk data version.
     * This should be called when multiple chunks are claimed or unclaimed at once.
     *
     * @param chunks The chunk positions that changed
     */
    public static void markChunksChanged(Collection<ChunkPos> chunks) {
        changedChunks.addAll(chunks);
        currentChunkVersion++;
    }
    
    /**
     * Synchronizes chunk data to all relevant players.
     * This method efficiently sends only the changed data to the players who need it.
     *
     * @param server The server instance
     */
    public static void syncChunkData(MinecraftServer server) {
        if (changedChunks.isEmpty()) {
            return;
        }
        
        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();
        
        // Determine which players need updates
        for (ServerPlayerEntity player : onlinePlayers) {
            UUID playerId = player.getUuid();
            
            // Check if this player needs an update
            if (shouldSendChunkUpdate(playerId)) {
                // Send chunk updates to the player
                sendChunkUpdates(player);
                
                // Update the player's known version
                updatePlayerChunkVersion(playerId);
            }
        }
        
        // Clear changed chunks after sync
        changedChunks.clear();
    }
    
    /**
     * Synchronizes a specific chunk to all relevant players.
     * This method is used when a single chunk is claimed or unclaimed.
     *
     * @param server The server instance
     * @param chunkPos The chunk position
     * @param town The town that owns the chunk, or null if the chunk is unclaimed
     * @param tag The tag applied to the chunk, or null if the chunk has no tag
     */
    public static void syncChunk(MinecraftServer server, ChunkPos chunkPos, Town town, ClaimTag tag) {
        // Mark the chunk as changed
        markChunkChanged(chunkPos);
        
        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();
        
        // Send update to all players
        for (ServerPlayerEntity player : onlinePlayers) {
            sendChunkUpdate(player, chunkPos, town, tag);
        }
        
        // Clear the changed chunk
        changedChunks.remove(chunkPos);
    }
    
    /**
     * Synchronizes multiple chunks to all relevant players.
     * This method is used when multiple chunks are claimed or unclaimed at once.
     *
     * @param server The server instance
     * @param chunks The chunk positions
     * @param town The town that owns the chunks, or null if the chunks are unclaimed
     * @param tags The tags applied to each chunk, or null if the chunks have no tags
     */
    public static void syncChunks(MinecraftServer server, Collection<ChunkPos> chunks, Town town, Map<ChunkPos, ClaimTag> tags) {
        // Mark the chunks as changed
        markChunksChanged(chunks);
        
        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();
        
        // Send update to all players
        for (ServerPlayerEntity player : onlinePlayers) {
            sendChunkBatchUpdate(player, chunks, town, tags);
        }
        
        // Clear the changed chunks
        changedChunks.removeAll(chunks);
    }
    
    /**
     * Determines if a player should receive chunk updates.
     *
     * @param playerId The player's UUID
     * @return True if the player should receive updates, false otherwise
     */
    private static boolean shouldSendChunkUpdate(UUID playerId) {
        int playerVersion = playerChunkVersions.getOrDefault(playerId, 0);
        return playerVersion < currentChunkVersion;
    }
    
    /**
     * Updates the player's known chunk data version.
     *
     * @param playerId The player's UUID
     */
    private static void updatePlayerChunkVersion(UUID playerId) {
        playerChunkVersions.put(playerId, currentChunkVersion);
    }
    
    /**
     * Sends chunk updates to a player.
     * This method sends all changed chunks to the player.
     *
     * @param player The player to send updates to
     */
    private static void sendChunkUpdates(ServerPlayerEntity player) {
        // If there are too many changed chunks, send a batch update
        if (changedChunks.size() > 10) {
            sendChunkBatchUpdate(player, changedChunks, null, null);
            return;
        }
        
        // Otherwise, send individual updates for each chunk
        for (ChunkPos chunkPos : changedChunks) {
            // Get the town and tag for this chunk
            Town town = ChunkPermissionManager.getInstance().getTownForChunk(chunkPos);
            ClaimTag tag = ChunkPermissionManager.getInstance().getTagForChunk(chunkPos);
            
            // Send the update
            sendChunkUpdate(player, chunkPos, town, tag);
        }
    }
    
    /**
     * Sends a chunk update to a player.
     *
     * @param player The player to send the update to
     * @param chunkPos The chunk position
     * @param town The town that owns the chunk, or null if the chunk is unclaimed
     * @param tag The tag applied to the chunk, or null if the chunk has no tag
     */
    private static void sendChunkUpdate(ServerPlayerEntity player, ChunkPos chunkPos, Town town, ClaimTag tag) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();
            
            // Write chunk position
            buf.writeInt(chunkPos.x);
            buf.writeInt(chunkPos.z);
            
            // Write if the chunk is claimed
            buf.writeBoolean(town != null);
            
            if (town != null) {
                // Write town UUID
                buf.writeUuid(town.getId());
                
                // Write town name
                buf.writeString(town.getName());
                
                // Write if the chunk has a tag
                buf.writeBoolean(tag != null);
                
                if (tag != null) {
                    // Write tag name
                    buf.writeString(tag.getName());
                    
                    // Write tag color
                    buf.writeInt(tag.getColor());
                    
                    // Write if the player is in the town
                    boolean playerInTown = town.getPlayers().contains(player.getUuid());
                    buf.writeBoolean(playerInTown);
                    
                    // Write permissions for non-members
                    boolean[] nonMemberPermissions = tag.getPermissionsForRank(null);
                    buf.writeInt(nonMemberPermissions.length);
                    for (boolean permission : nonMemberPermissions) {
                        buf.writeBoolean(permission);
                    }
                }
            }
            
            // Send packet to player
            NetworkManager.sendToPlayer(player, CHUNK_UPDATE, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent chunk update for " + chunkPos + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk update: " + e.getMessage());
        }
    }
    
    /**
     * Sends a batch update of chunks to a player.
     *
     * @param player The player to send the update to
     * @param chunks The chunk positions
     * @param town The town that owns the chunks, or null if the chunks are unclaimed
     * @param tags The tags applied to each chunk, or null if the chunks have no tags
     */
    private static void sendChunkBatchUpdate(ServerPlayerEntity player, Collection<ChunkPos> chunks, Town town, Map<ChunkPos, ClaimTag> tags) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();
            
            // Write chunk count
            buf.writeInt(chunks.size());
            
            // Write if all chunks have the same town
            boolean singleTown = town != null;
            buf.writeBoolean(singleTown);
            
            if (singleTown) {
                // Write town UUID
                buf.writeUuid(town.getId());
                
                // Write town name
                buf.writeString(town.getName());
                
                // Write if the player is in the town
                boolean playerInTown = town.getPlayers().contains(player.getUuid());
                buf.writeBoolean(playerInTown);
            }
            
            // Write chunk data
            for (ChunkPos chunkPos : chunks) {
                // Write chunk position
                buf.writeInt(chunkPos.x);
                buf.writeInt(chunkPos.z);
                
                if (!singleTown) {
                    // Get the town for this chunk
                    Town chunkTown = ChunkPermissionManager.getInstance().getTownForChunk(chunkPos);
                    
                    // Write if the chunk is claimed
                    buf.writeBoolean(chunkTown != null);
                    
                    if (chunkTown != null) {
                        // Write town UUID
                        buf.writeUuid(chunkTown.getId());
                        
                        // Write town name
                        buf.writeString(chunkTown.getName());
                        
                        // Write if the player is in the town
                        boolean playerInTown = chunkTown.getPlayers().contains(player.getUuid());
                        buf.writeBoolean(playerInTown);
                    }
                }
                
                // Get the tag for this chunk
                ClaimTag tag = tags != null ? tags.get(chunkPos) : ChunkPermissionManager.getInstance().getTagForChunk(chunkPos);
                
                // Write if the chunk has a tag
                buf.writeBoolean(tag != null);
                
                if (tag != null) {
                    // Write tag name
                    buf.writeString(tag.getName());
                    
                    // Write tag color
                    buf.writeInt(tag.getColor());
                    
                    // Write permissions for non-members
                    boolean[] nonMemberPermissions = tag.getPermissionsForRank(null);
                    buf.writeInt(nonMemberPermissions.length);
                    for (boolean permission : nonMemberPermissions) {
                        buf.writeBoolean(permission);
                    }
                }
            }
            
            // Send packet to player
            NetworkManager.sendToPlayer(player, CHUNK_BATCH_UPDATE, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent chunk batch update with " + chunks.size() + " chunks to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk batch update: " + e.getMessage());
        }
    }
    
    /**
     * Handles chunk update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkUpdate(MinecraftClient client, 
                                         net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                         PacketByteBuf buf, 
                                         PacketSender sender) {
        try {
            // Read chunk position
            int chunkX = buf.readInt();
            int chunkZ = buf.readInt();
            ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
            
            // Read if the chunk is claimed
            boolean isClaimed = buf.readBoolean();
            
            if (isClaimed) {
                // Read town UUID
                UUID townId = buf.readUuid();
                
                // Read town name
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                
                // Read if the chunk has a tag
                boolean hasTag = buf.readBoolean();
                
                if (hasTag) {
                    // Read tag name
                    String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    
                    // Read tag color
                    int tagColor = buf.readInt();
                    
                    // Read if the player is in the town
                    boolean playerInTown = buf.readBoolean();
                    
                    // Read permissions for non-members
                    int permissionCount = buf.readInt();
                    boolean[] nonMemberPermissions = new boolean[permissionCount];
                    for (int i = 0; i < permissionCount; i++) {
                        nonMemberPermissions[i] = buf.readBoolean();
                    }
                    
                    // Create tag
                    ClaimTag tag = new ClaimTag(tagName, tagColor);
                    
                    // Set permissions for non-members
                    for (int i = 0; i < nonMemberPermissions.length; i++) {
                        tag.getRankPermissions().setPermission(null, i, nonMemberPermissions[i]);
                    }
                    
                    // Get or create town
                    Town town = TownManager.getInstance().getTownById(townId);
                    
                    if (town == null) {
                        town = new Town(townId, townName);
                        TownManager.getInstance().addTown(town);
                    }
                    
                    // Update chunk data in ChunkPermissionManager
                    ChunkPermissionManager.getInstance().setChunkTag(chunkPos, town, tag);

                    // Update client cache
                    com.pokecobble.town.client.ClientChunkManager.getInstance().updateChunk(chunkPos, townId, tag, currentChunkVersion + 1);

                    // Fire sync event
                    com.pokecobble.town.client.ClientSyncManager.getInstance().onChunkUpdated(chunkPos, currentChunkVersion + 1);

                    // Update claim tool if active
                    if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                        com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, town, tag);
                    }
                } else {
                    // Chunk is claimed but has no tag
                    // Get or create town
                    Town town = TownManager.getInstance().getTownById(townId);
                    
                    if (town == null) {
                        town = new Town(townId, townName);
                        TownManager.getInstance().addTown(town);
                    }
                    
                    // Update chunk data in ChunkPermissionManager
                    ChunkPermissionManager.getInstance().setChunkTag(chunkPos, town, null);

                    // Update client cache
                    com.pokecobble.town.client.ClientChunkManager.getInstance().updateChunk(chunkPos, townId, null, currentChunkVersion + 1);

                    // Fire sync event
                    com.pokecobble.town.client.ClientSyncManager.getInstance().onChunkUpdated(chunkPos, currentChunkVersion + 1);

                    // Update claim tool if active
                    if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                        com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, town, null);
                    }
                }
            } else {
                // Chunk is not claimed, remove from ChunkPermissionManager
                ChunkPermissionManager.getInstance().removeChunkClaim(chunkPos);

                // Update client cache
                com.pokecobble.town.client.ClientChunkManager.getInstance().updateChunk(chunkPos, null, null, currentChunkVersion + 1);

                // Fire sync event
                com.pokecobble.town.client.ClientSyncManager.getInstance().onChunkUpdated(chunkPos, currentChunkVersion + 1);

                // Update claim tool if active
                if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                    com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, null, null);
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Received chunk update for " + chunkPos);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk update: " + e.getMessage());
        }
    }
    
    /**
     * Handles chunk batch update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkBatchUpdate(MinecraftClient client, 
                                              net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, 
                                              PacketSender sender) {
        try {
            // Read chunk count
            int chunkCount = buf.readInt();
            
            // Read if all chunks have the same town
            boolean singleTown = buf.readBoolean();
            
            // Town variables for single town mode
            UUID townId = null;
            String townName = null;
            boolean playerInTown = false;
            Town town = null;
            
            if (singleTown) {
                // Read town UUID
                townId = buf.readUuid();
                
                // Read town name
                townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                
                // Read if the player is in the town
                playerInTown = buf.readBoolean();
                
                // Get or create town
                town = TownManager.getInstance().getTownById(townId);
                
                if (town == null) {
                    town = new Town(townId, townName);
                    TownManager.getInstance().addTown(town);
                }
            }
            
            // Read chunk data
            for (int i = 0; i < chunkCount; i++) {
                // Read chunk position
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
                
                // Town variables for multi-town mode
                Town chunkTown = town;
                
                if (!singleTown) {
                    // Read if the chunk is claimed
                    boolean isClaimed = buf.readBoolean();
                    
                    if (isClaimed) {
                        // Read town UUID
                        UUID chunkTownId = buf.readUuid();
                        
                        // Read town name
                        String chunkTownName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                        
                        // Read if the player is in the town
                        boolean chunkPlayerInTown = buf.readBoolean();
                        
                        // Get or create town
                        chunkTown = TownManager.getInstance().getTownById(chunkTownId);
                        
                        if (chunkTown == null) {
                            chunkTown = new Town(chunkTownId, chunkTownName);
                            TownManager.getInstance().addTown(chunkTown);
                        }
                    } else {
                        chunkTown = null;
                    }
                }
                
                // Read if the chunk has a tag
                boolean hasTag = buf.readBoolean();
                
                if (hasTag) {
                    // Read tag name
                    String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    
                    // Read tag color
                    int tagColor = buf.readInt();
                    
                    // Read permissions for non-members
                    int permissionCount = buf.readInt();
                    boolean[] nonMemberPermissions = new boolean[permissionCount];
                    for (int j = 0; j < permissionCount; j++) {
                        nonMemberPermissions[j] = buf.readBoolean();
                    }
                    
                    // Create tag
                    ClaimTag tag = new ClaimTag(tagName, tagColor);
                    
                    // Set permissions for non-members
                    for (int j = 0; j < nonMemberPermissions.length; j++) {
                        tag.getRankPermissions().setPermission(null, j, nonMemberPermissions[j]);
                    }
                    
                    // Update chunk data in ChunkPermissionManager
                    if (chunkTown != null) {
                        ChunkPermissionManager.getInstance().setChunkTag(chunkPos, chunkTown, tag);
                        
                        // Update claim tool if active
                        if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                            com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, chunkTown, tag);
                        }
                    }
                } else if (chunkTown != null) {
                    // Chunk is claimed but has no tag
                    ChunkPermissionManager.getInstance().setChunkTag(chunkPos, chunkTown, null);
                    
                    // Update claim tool if active
                    if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                        com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, chunkTown, null);
                    }
                } else {
                    // Chunk is not claimed
                    ChunkPermissionManager.getInstance().removeChunkClaim(chunkPos);
                    
                    // Update claim tool if active
                    if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                        com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, null, null);
                    }
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Received chunk batch update with " + chunkCount + " chunks");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk batch update: " + e.getMessage());
        }
    }
    
    /**
     * Resets all player chunk versions.
     * This should be called when the server starts.
     */
    public static void resetAllPlayerChunkVersions() {
        playerChunkVersions.clear();
        currentChunkVersion = 0;
        changedChunks.clear();
    }
    
    /**
     * Removes a player from the version tracking.
     * This should be called when a player disconnects.
     *
     * @param playerId The player's UUID
     */
    public static void removePlayer(UUID playerId) {
        playerChunkVersions.remove(playerId);
    }

    /**
     * Synchronizes chunk data for a specific player.
     * This method sends all relevant chunk data to a specific player.
     *
     * @param server The server instance
     * @param playerId The player's UUID
     */
    public static void syncPlayerChunkData(MinecraftServer server, UUID playerId) {
        if (server == null || playerId == null) {
            return;
        }

        // Get the player
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return; // Player is not online
        }

        // Get all claimed chunks that the player should know about
        Collection<ChunkPos> relevantChunks = new ArrayList<>();

        // Add chunks from the player's town
        Town playerTown = TownManager.getInstance().getPlayerTown(playerId);
        if (playerTown != null) {
            relevantChunks.addAll(playerTown.getClaimedChunks());
        }

        // Add chunks from nearby areas (within render distance)
        // This is a simplified approach - in a real implementation you might want to
        // track which chunks the player can see based on their position

        // Send chunk data to the player
        if (!relevantChunks.isEmpty()) {
            // Group chunks by town for efficient sending
            Map<Town, Collection<ChunkPos>> chunksByTown = new HashMap<>();
            Map<ChunkPos, ClaimTag> chunkTags = new HashMap<>();

            for (ChunkPos chunk : relevantChunks) {
                Town town = ChunkPermissionManager.getInstance().getTownForChunk(chunk);
                if (town != null) {
                    chunksByTown.computeIfAbsent(town, k -> new ArrayList<>()).add(chunk);
                    ClaimTag tag = ChunkPermissionManager.getInstance().getTagForChunk(chunk);
                    if (tag != null) {
                        chunkTags.put(chunk, tag);
                    }
                }
            }

            // Send data for each town
            for (Map.Entry<Town, Collection<ChunkPos>> entry : chunksByTown.entrySet()) {
                syncChunks(server, entry.getValue(), entry.getKey(), chunkTags);
            }
        }

        // Update the player's known chunk version
        playerChunkVersions.put(playerId, currentChunkVersion);
    }
}
