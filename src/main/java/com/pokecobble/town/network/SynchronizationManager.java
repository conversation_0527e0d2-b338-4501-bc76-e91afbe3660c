package com.pokecobble.town.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import com.pokecobble.town.network.player.PlayerDataSynchronizer;
import com.pokecobble.town.network.election.ElectionNetworkHandler;
import com.pokecobble.town.network.money.MoneyNetworkHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages synchronization of data between server and client with proper error handling and retry mechanisms.
 */
public class SynchronizationManager {
    private static SynchronizationManager instance;
    
    // Retry mechanism
    private final Map<String, Integer> retryAttempts = new ConcurrentHashMap<>();
    private final Map<String, Long> lastRetryTime = new ConcurrentHashMap<>();
    private final ScheduledExecutorService retryExecutor = Executors.newScheduledThreadPool(2);
    
    // Synchronization tracking
    private final Map<UUID, Set<String>> playerSyncStatus = new ConcurrentHashMap<>();
    private final Map<String, Long> lastSyncTime = new ConcurrentHashMap<>();
    
    // Configuration
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000; // 1 second
    private static final long SYNC_TIMEOUT_MS = 30000; // 30 seconds
    private static final long PERIODIC_SYNC_INTERVAL_MS = 60000; // 1 minute
    
    // Sync types
    public static final String SYNC_TOWN_DATA = "town_data";
    public static final String SYNC_PLAYER_DATA = "player_data";
    public static final String SYNC_CHUNK_DATA = "chunk_data";
    public static final String SYNC_ELECTION_DATA = "election_data";
    public static final String SYNC_MONEY_DATA = "money_data";
    
    private SynchronizationManager() {
        // Start periodic sync checker
        retryExecutor.scheduleAtFixedRate(this::performPeriodicSync, 
                                        PERIODIC_SYNC_INTERVAL_MS, 
                                        PERIODIC_SYNC_INTERVAL_MS, 
                                        TimeUnit.MILLISECONDS);
    }
    
    public static SynchronizationManager getInstance() {
        if (instance == null) {
            instance = new SynchronizationManager();
        }
        return instance;
    }
    
    /**
     * Synchronizes all data for a player with retry mechanism.
     */
    public void syncAllPlayerData(MinecraftServer server, ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        
        // Initialize sync status for player
        playerSyncStatus.computeIfAbsent(playerId, k -> ConcurrentHashMap.newKeySet());
        
        // Sync each data type with error handling
        syncWithRetry(server, player, SYNC_TOWN_DATA, () -> {
            TownDataSynchronizer.syncPlayerTownData(server, playerId);
        });
        
        syncWithRetry(server, player, SYNC_PLAYER_DATA, () -> {
            PlayerDataSynchronizer.syncPlayerData(server, playerId);
        });
        
        syncWithRetry(server, player, SYNC_CHUNK_DATA, () -> {
            ChunkDataSynchronizer.syncChunkData(server);
        });
        
        syncWithRetry(server, player, SYNC_MONEY_DATA, () -> {
            // Trigger money balance sync if player is watching
            // This would be implemented in MoneyNetworkHandler
        });
    }
    
    /**
     * Synchronizes specific data type with retry mechanism.
     */
    public void syncWithRetry(MinecraftServer server, ServerPlayerEntity player, String syncType, Runnable syncAction) {
        String syncKey = player.getUuid() + ":" + syncType;
        
        try {
            // Execute sync action
            syncAction.run();
            
            // Mark as successful
            markSyncSuccess(player.getUuid(), syncType);
            
            // Clear retry attempts
            retryAttempts.remove(syncKey);
            lastRetryTime.remove(syncKey);
            
            Pokecobbleclaim.LOGGER.debug("Successfully synchronized " + syncType + " for player " + player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to synchronize " + syncType + " for player " + player.getName().getString() + ": " + e.getMessage());
            
            // Schedule retry
            scheduleRetry(server, player, syncType, syncAction, syncKey);
        }
    }
    
    /**
     * Schedules a retry for failed synchronization.
     */
    private void scheduleRetry(MinecraftServer server, ServerPlayerEntity player, String syncType, Runnable syncAction, String syncKey) {
        int attempts = retryAttempts.getOrDefault(syncKey, 0) + 1;
        retryAttempts.put(syncKey, attempts);
        
        if (attempts <= MAX_RETRY_ATTEMPTS) {
            long delay = RETRY_DELAY_MS * attempts; // Exponential backoff
            
            retryExecutor.schedule(() -> {
                // Check if player is still online
                if (server.getPlayerManager().getPlayer(player.getUuid()) != null) {
                    Pokecobbleclaim.LOGGER.info("Retrying synchronization of " + syncType + " for player " + player.getName().getString() + " (attempt " + attempts + ")");
                    syncWithRetry(server, player, syncType, syncAction);
                } else {
                    // Player disconnected, clean up
                    retryAttempts.remove(syncKey);
                    lastRetryTime.remove(syncKey);
                }
            }, delay, TimeUnit.MILLISECONDS);
            
            lastRetryTime.put(syncKey, System.currentTimeMillis());
        } else {
            Pokecobbleclaim.LOGGER.error("Max retry attempts reached for " + syncType + " synchronization for player " + player.getName().getString());
            
            // Mark as failed
            markSyncFailure(player.getUuid(), syncType);
            
            // Clean up
            retryAttempts.remove(syncKey);
            lastRetryTime.remove(syncKey);
        }
    }
    
    /**
     * Marks a synchronization as successful.
     */
    private void markSyncSuccess(UUID playerId, String syncType) {
        Set<String> syncStatus = playerSyncStatus.get(playerId);
        if (syncStatus != null) {
            syncStatus.add(syncType);
        }
        lastSyncTime.put(playerId + ":" + syncType, System.currentTimeMillis());
    }
    
    /**
     * Marks a synchronization as failed.
     */
    private void markSyncFailure(UUID playerId, String syncType) {
        Set<String> syncStatus = playerSyncStatus.get(playerId);
        if (syncStatus != null) {
            syncStatus.remove(syncType);
        }
    }
    
    /**
     * Checks if a player has all data synchronized.
     */
    public boolean isPlayerFullySynced(UUID playerId) {
        Set<String> syncStatus = playerSyncStatus.get(playerId);
        if (syncStatus == null) {
            return false;
        }
        
        return syncStatus.contains(SYNC_TOWN_DATA) &&
               syncStatus.contains(SYNC_PLAYER_DATA) &&
               syncStatus.contains(SYNC_CHUNK_DATA) &&
               syncStatus.contains(SYNC_MONEY_DATA);
    }
    
    /**
     * Gets the synchronization status for a player.
     */
    public Set<String> getPlayerSyncStatus(UUID playerId) {
        return new HashSet<>(playerSyncStatus.getOrDefault(playerId, Collections.emptySet()));
    }
    
    /**
     * Performs periodic synchronization checks.
     */
    private void performPeriodicSync() {
        long currentTime = System.currentTimeMillis();

        // Check for stale synchronizations
        for (Map.Entry<String, Long> entry : lastSyncTime.entrySet()) {
            if (currentTime - entry.getValue() > SYNC_TIMEOUT_MS) {
                String[] parts = entry.getKey().split(":");
                if (parts.length == 2) {
                    UUID playerId = UUID.fromString(parts[0]);
                    String syncType = parts[1];

                    Pokecobbleclaim.LOGGER.warn("Stale synchronization detected for player " + playerId + ", type: " + syncType);

                    // Mark as failed and remove from tracking
                    markSyncFailure(playerId, syncType);
                    lastSyncTime.remove(entry.getKey());
                }
            }
        }

        // Cleanup expired invitations
        try {
            // This would need access to the server instance
            // For now, we'll add a separate cleanup method that can be called from the server
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during periodic invitation cleanup: " + e.getMessage());
        }
    }
    
    /**
     * Cleans up synchronization data for a disconnected player.
     */
    public void cleanupPlayer(UUID playerId) {
        playerSyncStatus.remove(playerId);
        
        // Remove retry attempts and last retry times
        retryAttempts.entrySet().removeIf(entry -> entry.getKey().startsWith(playerId.toString()));
        lastRetryTime.entrySet().removeIf(entry -> entry.getKey().startsWith(playerId.toString()));
        lastSyncTime.entrySet().removeIf(entry -> entry.getKey().startsWith(playerId.toString()));
        
        Pokecobbleclaim.LOGGER.debug("Cleaned up synchronization data for player " + playerId);
    }
    
    /**
     * Forces a full resynchronization for a player.
     */
    public void forceResync(MinecraftServer server, ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        
        // Clear existing sync status
        playerSyncStatus.remove(playerId);
        
        // Clear retry attempts
        retryAttempts.entrySet().removeIf(entry -> entry.getKey().startsWith(playerId.toString()));
        lastRetryTime.entrySet().removeIf(entry -> entry.getKey().startsWith(playerId.toString()));
        lastSyncTime.entrySet().removeIf(entry -> entry.getKey().startsWith(playerId.toString()));
        
        // Perform full sync
        syncAllPlayerData(server, player);
        
        Pokecobbleclaim.LOGGER.info("Forced full resynchronization for player " + player.getName().getString());
    }
    
    /**
     * Gets synchronization statistics.
     */
    public Map<String, Object> getSyncStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalPlayers", playerSyncStatus.size());
        stats.put("activeRetries", retryAttempts.size());
        stats.put("lastSyncOperations", lastSyncTime.size());
        
        // Count fully synced players
        long fullySyncedPlayers = playerSyncStatus.values().stream()
                .mapToLong(syncStatus -> syncStatus.size() >= 4 ? 1 : 0)
                .sum();
        stats.put("fullySyncedPlayers", fullySyncedPlayers);
        
        return stats;
    }
    
    /**
     * Shuts down the synchronization manager.
     */
    public void shutdown() {
        retryExecutor.shutdown();
        try {
            if (!retryExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                retryExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            retryExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
