package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.command.BackupCommand;
import com.pokecobble.town.command.TownElectionCommand;
import com.pokecobble.town.command.TownAdminCommand;
import com.pokecobble.town.command.ChunkRenderCommand;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;

import static net.minecraft.server.command.CommandManager.literal;

/**
 * Registers and handles town-related commands.
 */
public class TownCommand {

    /**
     * Registers server-side town commands.
     */
    public static void registerServerCommands() {
        CommandRegistrationCallback.EVENT.register(TownCommand::register);
    }

    /**
     * Registers client-side town commands.
     * Implementation moved to TownClientCommand to avoid class loading issues on the server.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientCommands() {
        // Implementation moved to TownClientCommand class
        // to avoid class loading issues on the server
    }

    /**
     * Registers server-side town commands.
     *
     * @param dispatcher The command dispatcher
     * @param registryAccess The command registry access
     * @param environment The registration environment
     */
    private static void register(CommandDispatcher<ServerCommandSource> dispatcher,
                                CommandRegistryAccess registryAccess,
                                CommandManager.RegistrationEnvironment environment) {

        Pokecobbleclaim.LOGGER.info("Registering server-side town commands");

        // Register /town command
        dispatcher.register(literal("town")
                .executes(context -> {
                    // This will be executed on the server side
                    // We'll send a message to the client to open the screen
                    Pokecobbleclaim.LOGGER.info("Server command /town executed");
                    context.getSource().sendFeedback(() -> Text.literal("Opening town interface..."), false);

                    // The actual screen opening happens on the client side via the CommandMixin
                    return 1;
                }));

        // Register /ville command (alias for /town)
        dispatcher.register(literal("ville")
                .executes(context -> {
                    // This will be executed on the server side
                    Pokecobbleclaim.LOGGER.info("Server command /ville executed");
                    context.getSource().sendFeedback(() -> Text.literal("Opening town interface..."), false);

                    // The actual screen opening happens on the client side via the CommandMixin
                    return 1;
                }));

        // Register the town election command
        TownElectionCommand.register(dispatcher, registryAccess, environment);

        // Register the town admin commands
        TownAdminCommand.register(dispatcher, registryAccess, environment);

        // Register the backup command
        BackupCommand.register(dispatcher, registryAccess, environment);

        Pokecobbleclaim.LOGGER.info("Server-side town commands registered successfully");
    }




}
