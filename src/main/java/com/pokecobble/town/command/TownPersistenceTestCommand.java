package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.UUID;

/**
 * Test command to verify town persistence functionality.
 * This command allows testing of town creation, joining, leaving, and persistence.
 */
public class TownPersistenceTestCommand {

    /**
     * Registers the town persistence test commands.
     *
     * @param dispatcher The command dispatcher
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher) {
        dispatcher.register(CommandManager.literal("towntest")
                .requires(source -> source.hasPermissionLevel(2)) // Require OP level
                .then(CommandManager.literal("create")
                        .then(CommandManager.argument("name", com.mojang.brigadier.arguments.StringArgumentType.string())
                                .executes(TownPersistenceTestCommand::createTestTown)))
                .then(CommandManager.literal("join")
                        .then(CommandManager.argument("townname", com.mojang.brigadier.arguments.StringArgumentType.string())
                                .executes(TownPersistenceTestCommand::joinTestTown)))
                .then(CommandManager.literal("leave")
                        .executes(TownPersistenceTestCommand::leaveTestTown))
                .then(CommandManager.literal("status")
                        .executes(TownPersistenceTestCommand::showStatus))
                .then(CommandManager.literal("save")
                        .executes(TownPersistenceTestCommand::forceSave))
                .then(CommandManager.literal("reload")
                        .executes(TownPersistenceTestCommand::forceReload)));
    }

    /**
     * Creates a test town with the executing player as mayor.
     */
    private static int createTestTown(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            String townName = com.mojang.brigadier.arguments.StringArgumentType.getString(context, "name");

            // Check if player is already in a town
            UUID playerId = player.getUuid();
            if (TownManager.getInstance().getPlayerTownId(playerId) != null) {
                context.getSource().sendFeedback(() -> Text.literal("You are already in a town!").formatted(Formatting.RED), false);
                return 0;
            }

            // Create town with the player as mayor
            Town town = TownManager.getInstance().createTownWithMayor(townName, playerId);
            if (town != null) {
                context.getSource().sendFeedback(() -> Text.literal("Created test town: " + townName + " with you as mayor").formatted(Formatting.GREEN), false);
                Pokecobbleclaim.LOGGER.info("Test town created: " + townName + " by " + player.getName().getString());
                return 1;
            } else {
                context.getSource().sendFeedback(() -> Text.literal("Failed to create town (name might be taken)").formatted(Formatting.RED), false);
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("Error creating test town: " + e.getMessage()).formatted(Formatting.RED), false);
            Pokecobbleclaim.LOGGER.error("Error in createTestTown command: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Joins a test town.
     */
    private static int joinTestTown(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            String townName = com.mojang.brigadier.arguments.StringArgumentType.getString(context, "townname");

            UUID playerId = player.getUuid();

            // Check if player is already in a town
            if (TownManager.getInstance().getPlayerTownId(playerId) != null) {
                context.getSource().sendFeedback(() -> Text.literal("You are already in a town!").formatted(Formatting.RED), false);
                return 0;
            }

            // Find the town
            Town town = TownManager.getInstance().getTownByName(townName);
            if (town == null) {
                context.getSource().sendFeedback(() -> Text.literal("Town not found: " + townName).formatted(Formatting.RED), false);
                return 0;
            }

            // Add player to town
            boolean success = TownManager.getInstance().addPlayerToTown(playerId, town.getId());
            if (success) {
                context.getSource().sendFeedback(() -> Text.literal("Joined town: " + townName).formatted(Formatting.GREEN), false);
                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " joined test town: " + townName);
                return 1;
            } else {
                context.getSource().sendFeedback(() -> Text.literal("Failed to join town").formatted(Formatting.RED), false);
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("Error joining test town: " + e.getMessage()).formatted(Formatting.RED), false);
            Pokecobbleclaim.LOGGER.error("Error in joinTestTown command: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Leaves the current town.
     */
    private static int leaveTestTown(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            UUID playerId = player.getUuid();

            // Check if player is in a town
            UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
            if (townId == null) {
                context.getSource().sendFeedback(() -> Text.literal("You are not in a town!").formatted(Formatting.RED), false);
                return 0;
            }

            Town town = TownManager.getInstance().getTownById(townId);
            String townName = town != null ? town.getName() : "Unknown";

            // Remove player from town
            boolean success = TownManager.getInstance().removePlayerFromTown(playerId);
            if (success) {
                context.getSource().sendFeedback(() -> Text.literal("Left town: " + townName).formatted(Formatting.GREEN), false);
                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " left test town: " + townName);
                return 1;
            } else {
                context.getSource().sendFeedback(() -> Text.literal("Failed to leave town").formatted(Formatting.RED), false);
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("Error leaving test town: " + e.getMessage()).formatted(Formatting.RED), false);
            Pokecobbleclaim.LOGGER.error("Error in leaveTestTown command: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Shows the current town status of the player.
     */
    private static int showStatus(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            UUID playerId = player.getUuid();

            UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
            if (townId == null) {
                context.getSource().sendFeedback(() -> Text.literal("You are not in a town").formatted(Formatting.YELLOW), false);
            } else {
                Town town = TownManager.getInstance().getTownById(townId);
                if (town != null) {
                    TownPlayer townPlayer = town.getPlayer(playerId);
                    String rank = townPlayer != null ? townPlayer.getRank().name() : "UNKNOWN";
                    context.getSource().sendFeedback(() -> Text.literal("Town: " + town.getName() + " | Rank: " + rank + " | Players: " + town.getPlayerCount()).formatted(Formatting.GREEN), false);
                } else {
                    context.getSource().sendFeedback(() -> Text.literal("Town data inconsistent (ID: " + townId + ")").formatted(Formatting.RED), false);
                }
            }

            // Show total towns
            int totalTowns = TownManager.getInstance().getAllTowns().size();
            context.getSource().sendFeedback(() -> Text.literal("Total towns on server: " + totalTowns).formatted(Formatting.AQUA), false);

            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("Error showing status: " + e.getMessage()).formatted(Formatting.RED), false);
            Pokecobbleclaim.LOGGER.error("Error in showStatus command: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Forces a save of all data.
     */
    private static int forceSave(CommandContext<ServerCommandSource> context) {
        try {
            TownManager.getInstance().saveAllData();
            context.getSource().sendFeedback(() -> Text.literal("Forced save of all town data completed").formatted(Formatting.GREEN), false);
            Pokecobbleclaim.LOGGER.info("Forced save executed by " + context.getSource().getName());
            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("Error during forced save: " + e.getMessage()).formatted(Formatting.RED), false);
            Pokecobbleclaim.LOGGER.error("Error in forceSave command: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Forces a reload of all data.
     */
    private static int forceReload(CommandContext<ServerCommandSource> context) {
        try {
            com.pokecobble.town.data.TownDataStorage.loadTowns();
            context.getSource().sendFeedback(() -> Text.literal("Forced reload of all town data completed").formatted(Formatting.GREEN), false);
            Pokecobbleclaim.LOGGER.info("Forced reload executed by " + context.getSource().getName());
            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("Error during forced reload: " + e.getMessage()).formatted(Formatting.RED), false);
            Pokecobbleclaim.LOGGER.error("Error in forceReload command: " + e.getMessage());
            return 0;
        }
    }
}
