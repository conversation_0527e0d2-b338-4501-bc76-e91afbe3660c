package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.InviteNotification;
import net.minecraft.client.MinecraftClient;

/**
 * Manages opening and closing the town screen.
 */
public class TownScreenManager {

    /**
     * Opens the town screen using the normal Minecraft GUI.
     */
    public static void openTownScreen() {
        Pokecobbleclaim.LOGGER.info("Opening town screen");
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            // Check if there's a pending invitation
            if (InviteNotification.hasPendingInvite()) {
                // Open the invite response screen
                client.execute(() -> client.setScreen(new InviteResponseScreen(
                    client.currentScreen,
                    InviteNotification.getPendingInviteTownId(),
                    InviteNotification.getPendingInviteTownName()
                )));
            } else {
                // Open the normal town screen
                client.execute(() -> client.setScreen(new ModernTownScreen(client.currentScreen)));
            }
        }
    }
}
