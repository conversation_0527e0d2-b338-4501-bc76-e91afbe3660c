package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayerRank;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

/**
 * Confirmation screen for leaving a town.
 */
public class LeaveTownConfirmationScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private final boolean isMayor;

    // UI elements
    private ButtonWidget confirmButton;
    private ButtonWidget cancelButton;

    // Colors
    private static final int BACKGROUND_COLOR = 0xFF101010; // Solid dark background
    private static final int PANEL_COLOR = 0xFF202030; // Solid panel background
    private static final int BORDER_COLOR = 0xFF5555FF; // Blue border
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White text
    private static final int CONFIRM_BUTTON_COLOR = 0xFFE53935; // Red confirm button
    private static final int CANCEL_BUTTON_COLOR = 0xFF4CAF50; // Green cancel button

    public LeaveTownConfirmationScreen(Screen parent, Town town, boolean isMayor) {
        super(Text.literal("Leave Town"));
        this.parent = parent;
        this.town = town;
        this.isMayor = isMayor;
    }

    @Override
    protected void init() {
        super.init();

        int centerX = this.width / 2;
        int centerY = this.height / 2;

        // Create buttons
        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonSpacing = 10;

        // Confirm button (Leave Town)
        confirmButton = ButtonWidget.builder(Text.literal("Leave Town"), button -> {
            leaveTown();
            this.close();
        })
        .dimensions(centerX - buttonWidth - buttonSpacing/2, centerY + 40, buttonWidth, buttonHeight)
        .build();

        // Cancel button (Stay)
        cancelButton = ButtonWidget.builder(Text.literal("Stay"), button -> {
            this.close();
        })
        .dimensions(centerX + buttonSpacing/2, centerY + 40, buttonWidth, buttonHeight)
        .build();

        // Add buttons to the screen
        this.addDrawableChild(confirmButton);
        this.addDrawableChild(cancelButton);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw a solid dark background over the entire screen
        context.fill(0, 0, this.width, this.height, BACKGROUND_COLOR);

        // Draw a solid panel
        int panelWidth = 400;
        int panelHeight = 150;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;

        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, PANEL_COLOR);

        // Draw panel border
        context.drawBorder(panelX, panelY, panelWidth, panelHeight, BORDER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Leave Town").formatted(Formatting.BOLD),
                this.width / 2, panelY + 15, TEXT_COLOR);

        // Draw confirmation message
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Are you sure you want to leave " + town.getName() + "?"),
                this.width / 2, panelY + 40, TEXT_COLOR);

        // Draw additional warning for mayor
        if (isMayor) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("As the Mayor, leaving will trigger an election!").formatted(Formatting.GOLD),
                    this.width / 2, panelY + 60, TEXT_COLOR);

            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("Another player will be elected as the new Mayor.").formatted(Formatting.GOLD),
                    this.width / 2, panelY + 75, TEXT_COLOR);
        }

        // Draw buttons with custom colors
        super.render(context, mouseX, mouseY, delta);

        // Draw button overlays for custom colors
        drawButtonOverlay(context, confirmButton, CONFIRM_BUTTON_COLOR);
        drawButtonOverlay(context, cancelButton, CANCEL_BUTTON_COLOR);
    }

    /**
     * Draws a colored overlay on a button to customize its appearance.
     */
    private void drawButtonOverlay(DrawContext context, ButtonWidget button, int color) {
        int alpha = button.isHovered() ? 0xDD : 0xAA; // More opaque when hovered
        int overlayColor = (alpha << 24) | (color & 0x00FFFFFF);
        context.fill(button.getX(), button.getY(), button.getX() + button.getWidth(), button.getY() + button.getHeight(), overlayColor);

        // Re-draw the button text
        int textX = button.getX() + (button.getWidth() - this.textRenderer.getWidth(button.getMessage())) / 2;
        int textY = button.getY() + (button.getHeight() - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, button.getMessage(), textX, textY, 0xFFFFFFFF);
    }

    /**
     * Handles the player leaving the town.
     */
    private void leaveTown() {
        if (client.player == null) return;

        if (isMayor) {
            // Start an election
            TownManager.getInstance().startElection(town);
        }

        // Send leave request to server
        com.pokecobble.town.network.town.TownNetworkHandler.requestTownLeave();

        // Return to main menu
        client.setScreen(null);
    }

    @Override
    public void close() {
        MinecraftClient.getInstance().setScreen(parent);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}
