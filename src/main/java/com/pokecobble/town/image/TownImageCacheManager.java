package com.pokecobble.town.image;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.util.TownImageUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.util.Identifier;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * Enhanced town image cache manager with version tracking and efficient data transfer.
 */
@Environment(EnvType.CLIENT)
public class TownImageCacheManager {
    private static TownImageCacheManager instance;
    
    // Image cache: cache key -> texture identifier
    private final Map<String, Identifier> textureCache = new ConcurrentHashMap<>();
    
    // Image metadata cache: cache key -> metadata
    private final Map<String, ImageMetadata> metadataCache = new ConcurrentHashMap<>();
    
    // Image data cache: cache key -> compressed image data
    private final Map<String, byte[]> imageDataCache = new ConcurrentHashMap<>();
    
    // Version tracking: town ID -> image version
    private final Map<UUID, String> townImageVersions = new ConcurrentHashMap<>();
    
    // Cache statistics
    private final Map<String, Long> lastAccessTimes = new ConcurrentHashMap<>();
    private final Map<String, Integer> accessCounts = new ConcurrentHashMap<>();
    
    // Configuration
    private static final long CACHE_TIMEOUT_MS = 300000; // 5 minutes
    private static final int MAX_CACHE_SIZE = 100; // Maximum number of cached images
    private static final int MAX_IMAGE_SIZE_BYTES = 5 * 1024 * 1024; // 5MB per image
    
    private TownImageCacheManager() {
        // Start periodic cleanup
        startPeriodicCleanup();
    }
    
    public static TownImageCacheManager getInstance() {
        if (instance == null) {
            instance = new TownImageCacheManager();
        }
        return instance;
    }
    
    /**
     * Gets a cached texture for a town image.
     */
    public Identifier getCachedTexture(UUID townId, String imageName) {
        String cacheKey = generateCacheKey(townId, imageName);
        
        // Update access statistics
        updateAccessStats(cacheKey);
        
        // Check if texture is cached
        Identifier texture = textureCache.get(cacheKey);
        if (texture != null) {
            return texture;
        }
        
        // Try to load from disk cache
        return loadFromDiskCache(townId, imageName, cacheKey);
    }
    
    /**
     * Caches a texture for a town image.
     */
    public void cacheTexture(UUID townId, String imageName, Identifier texture, ImageMetadata metadata) {
        String cacheKey = generateCacheKey(townId, imageName);
        
        // Check cache size limits
        if (textureCache.size() >= MAX_CACHE_SIZE) {
            evictLeastRecentlyUsed();
        }
        
        // Cache the texture and metadata
        textureCache.put(cacheKey, texture);
        if (metadata != null) {
            metadataCache.put(cacheKey, metadata);
        }
        
        // Update access statistics
        updateAccessStats(cacheKey);
        
        // Update version tracking
        if (metadata != null) {
            townImageVersions.put(townId, metadata.getVersion());
        }
        
        Pokecobbleclaim.LOGGER.debug("Cached texture for town " + townId + ", image: " + imageName);
    }
    
    /**
     * Caches image data for efficient transfer.
     */
    public void cacheImageData(UUID townId, String imageName, byte[] imageData, ImageMetadata metadata) {
        String cacheKey = generateCacheKey(townId, imageName);
        
        // Validate image size
        if (imageData.length > MAX_IMAGE_SIZE_BYTES) {
            Pokecobbleclaim.LOGGER.warn("Image too large to cache: " + imageData.length + " bytes");
            return;
        }
        
        // Check cache size limits
        if (imageDataCache.size() >= MAX_CACHE_SIZE) {
            evictLeastRecentlyUsedData();
        }
        
        // Cache the image data and metadata
        imageDataCache.put(cacheKey, imageData);
        if (metadata != null) {
            metadataCache.put(cacheKey, metadata);
        }
        
        // Update access statistics
        updateAccessStats(cacheKey);
        
        // Update version tracking
        if (metadata != null) {
            townImageVersions.put(townId, metadata.getVersion());
        }
        
        Pokecobbleclaim.LOGGER.debug("Cached image data for town " + townId + ", image: " + imageName + " (" + imageData.length + " bytes)");
    }
    
    /**
     * Gets cached image data.
     */
    public byte[] getCachedImageData(UUID townId, String imageName) {
        String cacheKey = generateCacheKey(townId, imageName);
        
        // Update access statistics
        updateAccessStats(cacheKey);
        
        return imageDataCache.get(cacheKey);
    }
    
    /**
     * Gets cached image metadata.
     */
    public ImageMetadata getCachedMetadata(UUID townId, String imageName) {
        String cacheKey = generateCacheKey(townId, imageName);
        return metadataCache.get(cacheKey);
    }
    
    /**
     * Checks if an image is cached and up-to-date.
     */
    public boolean isImageCached(UUID townId, String imageName, String version) {
        String cacheKey = generateCacheKey(townId, imageName);
        
        // Check if texture is cached
        if (!textureCache.containsKey(cacheKey)) {
            return false;
        }
        
        // Check version
        ImageMetadata metadata = metadataCache.get(cacheKey);
        if (metadata == null) {
            return false;
        }
        
        return version.equals(metadata.getVersion());
    }
    
    /**
     * Invalidates cache for a specific town image.
     */
    public void invalidateImage(UUID townId, String imageName) {
        String cacheKey = generateCacheKey(townId, imageName);
        
        textureCache.remove(cacheKey);
        metadataCache.remove(cacheKey);
        imageDataCache.remove(cacheKey);
        lastAccessTimes.remove(cacheKey);
        accessCounts.remove(cacheKey);
        
        Pokecobbleclaim.LOGGER.debug("Invalidated cache for town " + townId + ", image: " + imageName);
    }
    
    /**
     * Invalidates all cache for a town.
     */
    public void invalidateTown(UUID townId) {
        String townPrefix = townId.toString() + ":";
        
        // Remove all entries for this town
        textureCache.entrySet().removeIf(entry -> entry.getKey().startsWith(townPrefix));
        metadataCache.entrySet().removeIf(entry -> entry.getKey().startsWith(townPrefix));
        imageDataCache.entrySet().removeIf(entry -> entry.getKey().startsWith(townPrefix));
        lastAccessTimes.entrySet().removeIf(entry -> entry.getKey().startsWith(townPrefix));
        accessCounts.entrySet().removeIf(entry -> entry.getKey().startsWith(townPrefix));
        
        townImageVersions.remove(townId);
        
        Pokecobbleclaim.LOGGER.debug("Invalidated all cache for town " + townId);
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        textureCache.clear();
        metadataCache.clear();
        imageDataCache.clear();
        lastAccessTimes.clear();
        accessCounts.clear();
        townImageVersions.clear();
        
        Pokecobbleclaim.LOGGER.debug("Cleared all image cache");
    }
    
    /**
     * Gets the current version for a town's image.
     */
    public String getTownImageVersion(UUID townId) {
        return townImageVersions.get(townId);
    }
    
    /**
     * Generates a cache key for a town image.
     */
    private String generateCacheKey(UUID townId, String imageName) {
        return townId.toString() + ":" + imageName;
    }
    
    /**
     * Updates access statistics for cache management.
     */
    private void updateAccessStats(String cacheKey) {
        lastAccessTimes.put(cacheKey, System.currentTimeMillis());
        accessCounts.put(cacheKey, accessCounts.getOrDefault(cacheKey, 0) + 1);
    }
    
    /**
     * Evicts the least recently used texture.
     */
    private void evictLeastRecentlyUsed() {
        String lruKey = null;
        long oldestTime = Long.MAX_VALUE;
        
        for (Map.Entry<String, Long> entry : lastAccessTimes.entrySet()) {
            if (entry.getValue() < oldestTime) {
                oldestTime = entry.getValue();
                lruKey = entry.getKey();
            }
        }
        
        if (lruKey != null) {
            textureCache.remove(lruKey);
            metadataCache.remove(lruKey);
            lastAccessTimes.remove(lruKey);
            accessCounts.remove(lruKey);
            
            Pokecobbleclaim.LOGGER.debug("Evicted LRU texture: " + lruKey);
        }
    }
    
    /**
     * Evicts the least recently used image data.
     */
    private void evictLeastRecentlyUsedData() {
        String lruKey = null;
        long oldestTime = Long.MAX_VALUE;
        
        for (Map.Entry<String, Long> entry : lastAccessTimes.entrySet()) {
            if (imageDataCache.containsKey(entry.getKey()) && entry.getValue() < oldestTime) {
                oldestTime = entry.getValue();
                lruKey = entry.getKey();
            }
        }
        
        if (lruKey != null) {
            imageDataCache.remove(lruKey);
            Pokecobbleclaim.LOGGER.debug("Evicted LRU image data: " + lruKey);
        }
    }
    
    /**
     * Loads texture from disk cache.
     */
    private Identifier loadFromDiskCache(UUID townId, String imageName, String cacheKey) {
        try {
            // Try to load using existing TownImageUtil
            Identifier texture = TownImageUtil.getTownImageTexture(townId.toString(), imageName);
            if (texture != null) {
                // Cache the loaded texture
                textureCache.put(cacheKey, texture);
                updateAccessStats(cacheKey);
                
                Pokecobbleclaim.LOGGER.debug("Loaded texture from disk cache: " + cacheKey);
                return texture;
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading texture from disk cache: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Starts periodic cache cleanup.
     */
    private void startPeriodicCleanup() {
        // This would be called periodically to clean up expired cache entries
        // Implementation depends on how the mod handles periodic tasks
        Pokecobbleclaim.LOGGER.debug("Started periodic cache cleanup");
    }
    
    /**
     * Performs cache cleanup.
     */
    public void performCleanup() {
        long currentTime = System.currentTimeMillis();
        List<String> expiredKeys = new ArrayList<>();
        
        // Find expired entries
        for (Map.Entry<String, Long> entry : lastAccessTimes.entrySet()) {
            if (currentTime - entry.getValue() > CACHE_TIMEOUT_MS) {
                expiredKeys.add(entry.getKey());
            }
        }
        
        // Remove expired entries
        for (String key : expiredKeys) {
            textureCache.remove(key);
            metadataCache.remove(key);
            imageDataCache.remove(key);
            lastAccessTimes.remove(key);
            accessCounts.remove(key);
        }
        
        if (!expiredKeys.isEmpty()) {
            Pokecobbleclaim.LOGGER.debug("Cleaned up " + expiredKeys.size() + " expired cache entries");
        }
    }
    
    /**
     * Gets cache statistics.
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("texturesCached", textureCache.size());
        stats.put("metadataCached", metadataCache.size());
        stats.put("imageDataCached", imageDataCache.size());
        stats.put("townVersionsTracked", townImageVersions.size());
        
        // Calculate total memory usage (approximate)
        long totalMemory = 0;
        for (byte[] data : imageDataCache.values()) {
            totalMemory += data.length;
        }
        stats.put("totalMemoryUsage", totalMemory);
        
        // Most accessed images
        String mostAccessed = accessCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("none");
        stats.put("mostAccessedImage", mostAccessed);
        
        return stats;
    }
    
    /**
     * Represents image metadata for caching.
     */
    public static class ImageMetadata {
        private final String version;
        private final long size;
        private final String hash;
        private final long timestamp;
        
        public ImageMetadata(String version, long size, String hash, long timestamp) {
            this.version = version;
            this.size = size;
            this.hash = hash;
            this.timestamp = timestamp;
        }
        
        public String getVersion() { return version; }
        public long getSize() { return size; }
        public String getHash() { return hash; }
        public long getTimestamp() { return timestamp; }
        
        /**
         * Generates a hash for image data.
         */
        public static String generateHash(byte[] data) {
            try {
                MessageDigest md = MessageDigest.getInstance("SHA-256");
                byte[] hash = md.digest(data);
                StringBuilder sb = new StringBuilder();
                for (byte b : hash) {
                    sb.append(String.format("%02x", b));
                }
                return sb.toString();
            } catch (NoSuchAlgorithmException e) {
                return String.valueOf(data.length); // Fallback to size
            }
        }
    }
}
