package com.pokecobble.town.test;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.config.TownSettingsManager;

import java.util.Map;
import java.util.UUID;

/**
 * Test class for town settings functionality.
 */
public class TownSettingsTest {
    
    /**
     * Tests basic town settings functionality.
     */
    public static void testTownSettings() {
        Pokecobbleclaim.LOGGER.info("Starting town settings test...");
        
        try {
            // Create a test town
            Town testTown = new Town("TestTown");
            TownManager.getInstance().addTown(testTown);
            
            UUID townId = testTown.getId();
            
            // Test getting default settings
            Map<String, Object> defaultSettings = TownSettingsManager.getTownSettings(townId);
            Pokecobbleclaim.LOGGER.info("Default town settings: " + defaultSettings);
            
            // Test setting individual values
            TownSettingsManager.setTownSetting(townId, "isOpen", false);
            TownSettingsManager.setTownSetting(townId, "allowPublicBuilding", true);
            
            // Test getting updated settings
            Map<String, Object> updatedSettings = TownSettingsManager.getTownSettings(townId);
            Pokecobbleclaim.LOGGER.info("Updated town settings: " + updatedSettings);
            
            // Verify the settings were applied to the town object
            boolean isOpen = testTown.getJoinType() == Town.JoinType.OPEN;
            Pokecobbleclaim.LOGGER.info("Town join type after setting: " + (isOpen ? "OPEN" : "CLOSED"));
            
            // Test setting multiple values at once
            Map<String, Object> newSettings = Map.of(
                "isOpen", true,
                "allowPublicBuilding", false
            );
            TownSettingsManager.setTownSettings(townId, newSettings);
            
            // Verify final settings
            Map<String, Object> finalSettings = TownSettingsManager.getTownSettings(townId);
            Pokecobbleclaim.LOGGER.info("Final town settings: " + finalSettings);
            
            // Verify the settings were applied to the town object
            boolean finalIsOpen = testTown.getJoinType() == Town.JoinType.OPEN;
            Pokecobbleclaim.LOGGER.info("Final town join type: " + (finalIsOpen ? "OPEN" : "CLOSED"));
            
            // Clean up
            TownSettingsManager.clearTownSettings(townId);
            
            Pokecobbleclaim.LOGGER.info("Town settings test completed successfully!");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Town settings test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Tests town settings with a player.
     */
    public static void testPlayerTownSettings(UUID playerId) {
        Pokecobbleclaim.LOGGER.info("Starting player town settings test for player: " + playerId);
        
        try {
            // Create a test town
            Town testTown = new Town("PlayerTestTown");
            TownManager.getInstance().addTown(testTown);
            
            // Add player to town
            TownManager.getInstance().addPlayerToTown(playerId, testTown.getId());
            
            // Test getting current player town settings (this would be called from client)
            Map<String, Object> playerTownSettings = TownSettingsManager.getCurrentPlayerTownSettings();
            Pokecobbleclaim.LOGGER.info("Player town settings: " + playerTownSettings);
            
            Pokecobbleclaim.LOGGER.info("Player town settings test completed successfully!");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Player town settings test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
