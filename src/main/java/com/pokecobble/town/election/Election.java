package com.pokecobble.town.election;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayerRank;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.*;

/**
 * Represents a mayoral election in a town.
 */
public class Election {
    private final Town town;
    private final long startTime;
    private final long endTime;
    private final Map<UUID, UUID> votes = new HashMap<>(); // Voter UUID -> Candidate UUID
    private final Map<UUID, Integer> voteCount = new HashMap<>(); // Candidate UUID -> Vote count
    private final Map<UUID, String> candidateStatements = new HashMap<>(); // Candidate UUID -> Campaign statement
    private final List<UUID> candidates = new ArrayList<>(); // List of official candidates
    private boolean isCompleted = false;
    private UUID winner = null;
    private String electionResults = ""; // Detailed election results for history

    // Election duration in milliseconds (24 hours)
    private static final long ELECTION_DURATION = 24 * 60 * 60 * 1000;

    /**
     * Creates a new election for a town.
     *
     * @param town The town holding the election
     */
    public Election(Town town) {
        this.town = town;
        this.startTime = System.currentTimeMillis();
        this.endTime = startTime + ELECTION_DURATION;

        // Initialize vote counts for all town members
        for (UUID playerId : town.getPlayers()) {
            voteCount.put(playerId, 0);
        }

        // Set town to closed during election
        town.setOpen(false);

        // Mark the town as being in an election
        town.setInElection(true);

        // Demote the current mayor to member
        demoteCurrentMayor();

        // Notify all players about the election
        notifyElectionStart();
    }

    /**
     * Creates a new election for a town with a specific end time.
     * This constructor is used by the network code.
     *
     * @param town The town holding the election
     * @param endTime The time when the election will end
     */
    public Election(Town town, long endTime) {
        this.town = town;
        this.startTime = System.currentTimeMillis();
        this.endTime = endTime;

        // Initialize vote counts for all town members
        for (UUID playerId : town.getPlayers()) {
            voteCount.put(playerId, 0);
        }

        // Set town to closed during election
        town.setOpen(false);

        // Mark the town as being in an election
        town.setInElection(true);
    }

    /**
     * Demotes the current mayor to a regular member.
     */
    private void demoteCurrentMayor() {
        // Find the current mayor
        for (UUID playerId : town.getPlayers()) {
            if (town.getPlayerRank(playerId) == TownPlayerRank.OWNER) {
                // Demote the mayor to a regular member
                town.setPlayerRank(playerId, TownPlayerRank.MEMBER);
                break;
            }
        }
    }

    /**
     * Registers a player as a candidate in the election.
     *
     * @param playerId The UUID of the player to register
     * @param statement The candidate's campaign statement
     * @return True if registration was successful, false otherwise
     */
    public boolean registerCandidate(UUID playerId, String statement) {
        // Check if the election is still ongoing
        if (isCompleted || System.currentTimeMillis() > endTime) {
            return false;
        }

        // Check if the player is a town member
        if (!town.getPlayers().contains(playerId)) {
            return false;
        }

        // Add to candidates list if not already there
        if (!candidates.contains(playerId)) {
            candidates.add(playerId);
        }

        // Store the campaign statement
        candidateStatements.put(playerId, statement);

        return true;
    }

    /**
     * Gets a candidate's campaign statement.
     *
     * @param candidateId The UUID of the candidate
     * @return The campaign statement, or an empty string if none exists
     */
    public String getCandidateStatement(UUID candidateId) {
        return candidateStatements.getOrDefault(candidateId, "");
    }

    /**
     * Gets the list of registered candidates.
     *
     * @return The list of candidate UUIDs
     */
    public List<UUID> getCandidates() {
        return Collections.unmodifiableList(candidates);
    }

    /**
     * Adds a candidate to the election with a specific vote count.
     * This method is used by the network code.
     *
     * @param candidateId The UUID of the candidate
     * @param votes The number of votes for the candidate
     */
    public void addCandidate(UUID candidateId, int votes) {
        if (!candidates.contains(candidateId)) {
            candidates.add(candidateId);
        }
        voteCount.put(candidateId, votes);
    }

    /**
     * Checks if a player is a candidate in the election.
     *
     * @param playerId The UUID of the player
     * @return True if the player is a candidate, false otherwise
     */
    public boolean isCandidate(UUID playerId) {
        return candidates.contains(playerId);
    }

    /**
     * Casts a vote for a candidate.
     *
     * @param voter The player casting the vote
     * @param candidate The candidate being voted for
     * @return True if the vote was cast successfully, false otherwise
     */
    public boolean castVote(UUID voter, UUID candidate) {
        // Check if the election is still ongoing
        if (isCompleted || System.currentTimeMillis() > endTime) {
            return false;
        }

        // Check if the voter has already voted
        if (votes.containsKey(voter)) {
            return false;
        }

        // Check if the voter and candidate are town members
        if (!town.getPlayers().contains(voter) || !town.getPlayers().contains(candidate)) {
            return false;
        }

        // Record the vote
        votes.put(voter, candidate);

        // Update vote count
        voteCount.put(candidate, voteCount.getOrDefault(candidate, 0) + 1);

        return true;
    }

    /**
     * Sets a player's vote in the election.
     * This method is used by the network code.
     *
     * @param voter The UUID of the voter
     * @param candidate The UUID of the candidate
     */
    public void setPlayerVote(UUID voter, UUID candidate) {
        votes.put(voter, candidate);
    }

    /**
     * Gets the candidate a player voted for.
     *
     * @param voter The UUID of the voter
     * @return The UUID of the candidate, or null if the player hasn't voted
     */
    public UUID getPlayerVote(UUID voter) {
        return votes.get(voter);
    }

    /**
     * Adds a vote to a candidate's count.
     * This method is used by the network code.
     *
     * @param candidate The UUID of the candidate
     */
    public void addVote(UUID candidate) {
        voteCount.put(candidate, voteCount.getOrDefault(candidate, 0) + 1);
    }

    /**
     * Checks if the election has ended and processes the results if needed.
     *
     * @return True if the election has ended, false otherwise
     */
    public boolean checkElectionEnd() {
        if (isCompleted) {
            return true;
        }

        if (System.currentTimeMillis() > endTime) {
            completeElectionAuto();
            return true;
        }

        return false;
    }

    /**
     * Completes the election and determines the winner.
     * Called automatically when the election timer expires.
     */
    private void completeElectionAuto() {
        if (isCompleted) {
            return;
        }

        completeElectionInternal();
    }

    /**
     * Forcibly completes the election.
     * This can be called by server operators to end an election early.
     */
    public void completeElection() {
        completeElectionInternal();
    }

    /**
     * Internal method to complete the election.
     */
    private void completeElectionInternal() {
        StringBuilder results = new StringBuilder();
        results.append("Election Results for ").append(town.getName()).append("\n");
        results.append("Date: ").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date())).append("\n");
        results.append("Total Votes Cast: ").append(votes.size()).append("\n\n");

        // Find the candidate with the most votes
        int maxVotes = -1;
        List<UUID> topCandidates = new ArrayList<>();

        // Sort candidates by vote count
        List<Map.Entry<UUID, Integer>> sortedVotes = new ArrayList<>(voteCount.entrySet());
        sortedVotes.sort(Map.Entry.<UUID, Integer>comparingByValue().reversed());

        // Add vote counts to results
        results.append("Vote Counts:\n");
        for (Map.Entry<UUID, Integer> entry : sortedVotes) {
            String candidateName = getPlayerName(entry.getKey());
            int votes = entry.getValue();
            results.append(candidateName).append(": ").append(votes).append(" votes\n");

            // Track top candidates
            if (votes > maxVotes) {
                maxVotes = votes;
                topCandidates.clear();
                topCandidates.add(entry.getKey());
            } else if (votes == maxVotes) {
                topCandidates.add(entry.getKey());
            }
        }

        results.append("\n");

        // If there's a tie, randomly select a winner
        if (topCandidates.size() > 1) {
            results.append("Tie detected between ").append(topCandidates.size()).append(" candidates.\n");
            Random random = new Random();
            winner = topCandidates.get(random.nextInt(topCandidates.size()));
            results.append("Winner selected by random tie-breaker: ").append(getPlayerName(winner)).append("\n");
        } else if (topCandidates.size() == 1) {
            winner = topCandidates.get(0);
            results.append("Winner: ").append(getPlayerName(winner)).append(" with ").append(maxVotes).append(" votes\n");
        } else {
            // No votes cast, randomly select a town member
            results.append("No votes were cast. Selecting random town member as mayor.\n");
            List<UUID> townMembers = town.getPlayers();
            if (!townMembers.isEmpty()) {
                Random random = new Random();
                winner = townMembers.get(random.nextInt(townMembers.size()));
                results.append("Randomly selected mayor: ").append(getPlayerName(winner)).append("\n");
            } else {
                results.append("No town members available to select as mayor.\n");
            }
        }

        // Update town with new mayor
        if (winner != null) {
            // Set the winner as the new mayor
            town.setPlayerRank(winner, TownPlayerRank.OWNER);
            results.append("\nNew mayor ").append(getPlayerName(winner)).append(" has been appointed.\n");

            // Notify all players about the election results
            notifyElectionResults();
        }

        // Store the election results
        electionResults = results.toString();

        // Mark the election as completed
        isCompleted = true;

        // Reset the town's election status
        town.setInElection(false);

        // Reopen the town
        town.setOpen(true);
    }

    /**
     * Notifies all players about the start of the election.
     */
    private void notifyElectionStart() {
        // In a real implementation, this would send a message to all players
        // For now, we'll just print to the console
        System.out.println("Election started in town: " + town.getName());
    }

    /**
     * Notifies all players about the election results.
     */
    private void notifyElectionResults() {
        // In a real implementation, this would send a message to all players
        // For now, we'll just print to the console
        System.out.println("Election completed in town: " + town.getName() + ", Winner: " + winner);
    }

    /**
     * Sends a notification to a player about the election.
     *
     * @param player The player to notify
     */
    public void notifyPlayer(PlayerEntity player) {
        if (isCompleted) {
            // Election is over
            player.sendMessage(Text.literal("The election in " + town.getName() + " has ended.")
                    .formatted(Formatting.GOLD), false);

            if (winner != null) {
                player.sendMessage(Text.literal("The new Mayor is: " + getPlayerName(winner))
                        .formatted(Formatting.GOLD), false);
            }
        } else {
            // Election is ongoing
            long timeLeft = endTime - System.currentTimeMillis();
            long hoursLeft = timeLeft / (60 * 60 * 1000);

            player.sendMessage(Text.literal("An election is in progress in " + town.getName() + "!")
                    .formatted(Formatting.GOLD), false);
            player.sendMessage(Text.literal("Time remaining: " + hoursLeft + " hours")
                    .formatted(Formatting.GOLD), false);
            player.sendMessage(Text.literal("Use the Vote Mayor button to cast your vote.")
                    .formatted(Formatting.GOLD), false);
        }
    }

    /**
     * Gets the name of a player from their UUID.
     *
     * @param playerId The player's UUID
     * @return The player's name, or "Unknown Player" if not found
     */
    private String getPlayerName(UUID playerId) {
        // In a real implementation, this would look up the player's name
        // For now, we'll just return a placeholder
        return "Player " + playerId.toString().substring(0, 8);
    }

    /**
     * Gets the town holding the election.
     *
     * @return The town
     */
    public Town getTown() {
        return town;
    }

    /**
     * Gets the time when the election started.
     *
     * @return The start time in milliseconds
     */
    public long getStartTime() {
        return startTime;
    }

    /**
     * Gets the time when the election will end.
     *
     * @return The end time in milliseconds
     */
    public long getEndTime() {
        return endTime;
    }

    /**
     * Gets the remaining time in the election.
     *
     * @return The remaining time in milliseconds
     */
    public long getRemainingTime() {
        return Math.max(0, endTime - System.currentTimeMillis());
    }

    /**
     * Gets the votes cast in the election.
     *
     * @return A map of voter UUIDs to candidate UUIDs
     */
    public Map<UUID, UUID> getVotes() {
        return Collections.unmodifiableMap(votes);
    }

    /**
     * Gets the vote counts for each candidate.
     *
     * @return A map of candidate UUIDs to vote counts
     */
    public Map<UUID, Integer> getVoteCount() {
        return Collections.unmodifiableMap(voteCount);
    }

    /**
     * Gets the sorted list of candidates by vote count.
     *
     * @return A list of candidate UUIDs sorted by vote count (descending)
     */
    public List<UUID> getSortedCandidates() {
        List<UUID> candidates = new ArrayList<>(voteCount.keySet());
        candidates.sort((a, b) -> voteCount.get(b) - voteCount.get(a));
        return candidates;
    }

    /**
     * Checks if a player has already voted.
     *
     * @param playerId The player's UUID
     * @return True if the player has voted, false otherwise
     */
    public boolean hasVoted(UUID playerId) {
        return votes.containsKey(playerId);
    }

    /**
     * Checks if the election is completed.
     *
     * @return True if the election is completed, false otherwise
     */
    public boolean isCompleted() {
        return isCompleted;
    }

    /**
     * Gets the winner of the election.
     *
     * @return The winner's UUID, or null if the election is not completed
     */
    public UUID getWinner() {
        return winner;
    }

    /**
     * Gets the detailed election results.
     *
     * @return The election results as a formatted string
     */
    public String getElectionResults() {
        return electionResults;
    }

    /**
     * Gets a player's name from their UUID (updated implementation).
     *
     * @param playerId The player's UUID
     * @return The player's name, or "Unknown Player" if not found
     */
    private String getPlayerNameById(UUID playerId) {
        if (playerId == null) {
            return "Unknown Player";
        }

        // Try to get the player from the town
        for (UUID playerUuid : town.getPlayers()) {
            if (playerUuid.equals(playerId)) {
                return town.getPlayerName(playerUuid);
            }
        }

        return "Unknown Player";
    }

    /**
     * Checks if the election is currently active (not completed and not expired).
     *
     * @return True if the election is active, false otherwise
     */
    public boolean isActive() {
        return !isCompleted && System.currentTimeMillis() <= endTime;
    }

    /**
     * Gets the ID of the town holding the election.
     *
     * @return The town's UUID
     */
    public UUID getTownId() {
        return town.getId();
    }

    /**
     * Gets the type of election.
     * For now, all elections are MAYOR elections.
     *
     * @return The election type
     */
    public ElectionType getType() {
        return ElectionType.MAYOR;
    }

    /**
     * Gets the time remaining in the election.
     * This is an alias for getRemainingTime() for compatibility.
     *
     * @return The remaining time in milliseconds
     */
    public long getTimeRemaining() {
        return getRemainingTime();
    }

    /**
     * Gets the total number of votes cast in the election.
     *
     * @return The total vote count
     */
    public int getTotalVotes() {
        return votes.size();
    }

    /**
     * Gets the list of players eligible to vote in the election.
     *
     * @return The list of eligible voter UUIDs
     */
    public List<UUID> getEligibleVoters() {
        return new ArrayList<>(town.getPlayerIds());
    }

    /**
     * Gets the voter turnout percentage.
     *
     * @return The turnout percentage (0.0 to 100.0)
     */
    public double getTurnoutPercentage() {
        List<UUID> eligibleVoters = getEligibleVoters();
        if (eligibleVoters.isEmpty()) {
            return 0.0;
        }
        return (double) getTotalVotes() / eligibleVoters.size() * 100.0;
    }

    /**
     * Enum for election types.
     */
    public enum ElectionType {
        MAYOR
    }
}
