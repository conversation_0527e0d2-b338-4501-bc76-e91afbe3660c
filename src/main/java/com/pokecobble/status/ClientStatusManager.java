package com.pokecobble.status;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.ClientSyncManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.network.PacketByteBuf;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * Client-side manager for real-time status updates.
 * Handles subscription to status updates and caches received status data.
 */
@Environment(EnvType.CLIENT)
public class ClientStatusManager {
    private static ClientStatusManager instance;
    
    // Status data cache
    private final Map<String, RealTimeStatusManager.StatusData> statusCache = new ConcurrentHashMap<>();
    
    // Status change listeners
    private final Map<String, Set<Consumer<RealTimeStatusManager.StatusData>>> statusListeners = new ConcurrentHashMap<>();
    
    // Subscription tracking
    private final Set<String> activeSubscriptions = new HashSet<>();
    
    // Configuration
    private static final long CACHE_TIMEOUT_MS = 60000; // 1 minute cache timeout
    
    private final Gson gson = new Gson();
    
    private ClientStatusManager() {
        setupNetworkHandlers();
    }
    
    public static ClientStatusManager getInstance() {
        if (instance == null) {
            instance = new ClientStatusManager();
        }
        return instance;
    }
    
    /**
     * Sets up network packet handlers.
     */
    private void setupNetworkHandlers() {
        // Register status update handler
        ClientPlayNetworking.registerGlobalReceiver(RealTimeStatusManager.STATUS_UPDATE, this::handleStatusUpdate);
    }
    
    /**
     * Handles status update from server.
     */
    private void handleStatusUpdate(net.minecraft.client.MinecraftClient client,
                                  net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                  PacketByteBuf buf,
                                  net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            String statusType = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            long timestamp = buf.readLong();
            String jsonData = buf.readString(NetworkConstants.MAX_STRING_LENGTH * 8);
            
            // Parse JSON data
            Type mapType = new TypeToken<Map<String, Object>>(){}.getType();
            Map<String, Object> data = gson.fromJson(jsonData, mapType);
            
            // Create status data
            RealTimeStatusManager.StatusData statusData = new RealTimeStatusManager.StatusData(statusType, data, timestamp);
            
            // Update cache
            statusCache.put(statusType, statusData);
            
            // Notify listeners
            notifyStatusListeners(statusType, statusData);
            
            // Fire global status update event
            ClientSyncManager.getInstance().fireEvent("status_updated", Map.of(
                "statusType", statusType,
                "data", data,
                "timestamp", timestamp
            ));
            
            Pokecobbleclaim.LOGGER.debug("Received status update: " + statusType + " (timestamp: " + timestamp + ")");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling status update: " + e.getMessage());
        }
    }
    
    /**
     * Subscribes to a status type.
     */
    public void subscribeToStatus(String statusType) {
        if (!activeSubscriptions.contains(statusType)) {
            activeSubscriptions.add(statusType);
            sendSubscriptionRequest(statusType, true);
            Pokecobbleclaim.LOGGER.debug("Subscribed to status: " + statusType);
        }
    }
    
    /**
     * Unsubscribes from a status type.
     */
    public void unsubscribeFromStatus(String statusType) {
        if (activeSubscriptions.contains(statusType)) {
            activeSubscriptions.remove(statusType);
            sendSubscriptionRequest(statusType, false);
            Pokecobbleclaim.LOGGER.debug("Unsubscribed from status: " + statusType);
        }
    }
    
    /**
     * Sends subscription request to server.
     */
    private void sendSubscriptionRequest(String statusType, boolean subscribe) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString(statusType, NetworkConstants.MAX_STRING_LENGTH);
            buf.writeBoolean(subscribe);
            
            NetworkManager.sendToServer(RealTimeStatusManager.STATUS_SUBSCRIPTION, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending subscription request: " + e.getMessage());
        }
    }
    
    /**
     * Gets cached status data.
     */
    public RealTimeStatusManager.StatusData getStatus(String statusType) {
        RealTimeStatusManager.StatusData data = statusCache.get(statusType);
        
        // Check if data is stale
        if (data != null && System.currentTimeMillis() - data.timestamp > CACHE_TIMEOUT_MS) {
            statusCache.remove(statusType);
            return null;
        }
        
        return data;
    }
    
    /**
     * Gets online players status.
     */
    public OnlinePlayersStatus getOnlinePlayersStatus() {
        RealTimeStatusManager.StatusData data = getStatus(RealTimeStatusManager.STATUS_ONLINE_PLAYERS);
        return data != null ? new OnlinePlayersStatus(data.data) : null;
    }
    
    /**
     * Gets town activity status.
     */
    public TownActivityStatus getTownActivityStatus() {
        RealTimeStatusManager.StatusData data = getStatus(RealTimeStatusManager.STATUS_TOWN_ACTIVITY);
        return data != null ? new TownActivityStatus(data.data) : null;
    }
    
    /**
     * Gets election progress status.
     */
    public ElectionProgressStatus getElectionProgressStatus() {
        RealTimeStatusManager.StatusData data = getStatus(RealTimeStatusManager.STATUS_ELECTION_PROGRESS);
        return data != null ? new ElectionProgressStatus(data.data) : null;
    }
    
    /**
     * Gets server stats status.
     */
    public ServerStatsStatus getServerStatsStatus() {
        RealTimeStatusManager.StatusData data = getStatus(RealTimeStatusManager.STATUS_SERVER_STATS);
        return data != null ? new ServerStatsStatus(data.data) : null;
    }
    
    /**
     * Adds a status change listener.
     */
    public void addStatusListener(String statusType, Consumer<RealTimeStatusManager.StatusData> listener) {
        statusListeners.computeIfAbsent(statusType, k -> new HashSet<>()).add(listener);
    }
    
    /**
     * Removes a status change listener.
     */
    public void removeStatusListener(String statusType, Consumer<RealTimeStatusManager.StatusData> listener) {
        Set<Consumer<RealTimeStatusManager.StatusData>> listeners = statusListeners.get(statusType);
        if (listeners != null) {
            listeners.remove(listener);
            if (listeners.isEmpty()) {
                statusListeners.remove(statusType);
            }
        }
    }
    
    /**
     * Notifies status listeners.
     */
    private void notifyStatusListeners(String statusType, RealTimeStatusManager.StatusData statusData) {
        Set<Consumer<RealTimeStatusManager.StatusData>> listeners = statusListeners.get(statusType);
        if (listeners != null) {
            for (Consumer<RealTimeStatusManager.StatusData> listener : listeners) {
                try {
                    listener.accept(statusData);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in status listener: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Clears all cached data and subscriptions.
     */
    public void clearAll() {
        statusCache.clear();
        activeSubscriptions.clear();
        statusListeners.clear();
    }
    
    /**
     * Gets status cache statistics.
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedStatusTypes", statusCache.size());
        stats.put("activeSubscriptions", activeSubscriptions.size());
        stats.put("statusListeners", statusListeners.size());
        
        // Count total listeners
        int totalListeners = statusListeners.values().stream()
                .mapToInt(Set::size)
                .sum();
        stats.put("totalListeners", totalListeners);
        
        return stats;
    }
    
    /**
     * Wrapper class for online players status.
     */
    public static class OnlinePlayersStatus {
        private final Map<String, Object> data;
        
        public OnlinePlayersStatus(Map<String, Object> data) {
            this.data = data;
        }
        
        public int getPlayerCount() {
            return ((Double) data.getOrDefault("count", 0.0)).intValue();
        }
        
        public int getMaxPlayers() {
            return ((Double) data.getOrDefault("maxPlayers", 0.0)).intValue();
        }
        
        @SuppressWarnings("unchecked")
        public List<Map<String, Object>> getPlayers() {
            return (List<Map<String, Object>>) data.getOrDefault("players", new ArrayList<>());
        }
        
        public long getTimestamp() {
            return ((Double) data.getOrDefault("timestamp", 0.0)).longValue();
        }
    }
    
    /**
     * Wrapper class for town activity status.
     */
    public static class TownActivityStatus {
        private final Map<String, Object> data;
        
        public TownActivityStatus(Map<String, Object> data) {
            this.data = data;
        }
        
        @SuppressWarnings("unchecked")
        public List<Map<String, Object>> getTowns() {
            return (List<Map<String, Object>>) data.getOrDefault("towns", new ArrayList<>());
        }
        
        public int getTotalTowns() {
            return ((Double) data.getOrDefault("totalTowns", 0.0)).intValue();
        }
        
        public long getTimestamp() {
            return ((Double) data.getOrDefault("timestamp", 0.0)).longValue();
        }
    }
    
    /**
     * Wrapper class for election progress status.
     */
    public static class ElectionProgressStatus {
        private final Map<String, Object> data;
        
        public ElectionProgressStatus(Map<String, Object> data) {
            this.data = data;
        }
        
        @SuppressWarnings("unchecked")
        public List<Map<String, Object>> getActiveElections() {
            return (List<Map<String, Object>>) data.getOrDefault("activeElections", new ArrayList<>());
        }
        
        public int getTotalActiveElections() {
            return ((Double) data.getOrDefault("totalActiveElections", 0.0)).intValue();
        }
        
        public long getTimestamp() {
            return ((Double) data.getOrDefault("timestamp", 0.0)).longValue();
        }
    }
    
    /**
     * Wrapper class for server stats status.
     */
    public static class ServerStatsStatus {
        private final Map<String, Object> data;
        
        public ServerStatsStatus(Map<String, Object> data) {
            this.data = data;
        }
        
        public long getMemoryUsed() {
            return ((Double) data.getOrDefault("memoryUsed", 0.0)).longValue();
        }
        
        public long getMemoryTotal() {
            return ((Double) data.getOrDefault("memoryTotal", 0.0)).longValue();
        }
        
        public double getMemoryUsagePercent() {
            return (Double) data.getOrDefault("memoryUsagePercent", 0.0);
        }
        
        public long getUptime() {
            return ((Double) data.getOrDefault("uptime", 0.0)).longValue();
        }
        
        public double getTps() {
            return (Double) data.getOrDefault("tps", 20.0);
        }
        
        public long getTimestamp() {
            return ((Double) data.getOrDefault("timestamp", 0.0)).longValue();
        }
    }
}
