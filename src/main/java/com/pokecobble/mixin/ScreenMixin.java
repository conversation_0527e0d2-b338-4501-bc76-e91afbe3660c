package com.pokecobble.mixin;

import com.pokecobble.ui.ScreenLifecycleTracker;
import net.minecraft.client.gui.screen.Screen;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to automatically track screen lifecycle events for UI data refresh.
 */
@Mixin(Screen.class)
public class ScreenMixin {
    
    /**
     * Tracks when a screen is initialized.
     */
    @Inject(method = "init()V", at = @At("TAIL"))
    private void onScreenInit(CallbackInfo ci) {
        try {
            Screen screen = (Screen) (Object) this;
            ScreenLifecycleTracker.getInstance().updateCurrentScreen();
        } catch (Exception e) {
            // Silently ignore errors to avoid breaking screen initialization
        }
    }
    
    /**
     * Tracks when a screen is removed.
     */
    @Inject(method = "removed()V", at = @At("HEAD"))
    private void onScreenRemoved(CallbackInfo ci) {
        try {
            ScreenLifecycleTracker.getInstance().updateCurrentScreen();
        } catch (Exception e) {
            // Silently ignore errors to avoid breaking screen removal
        }
    }
}
