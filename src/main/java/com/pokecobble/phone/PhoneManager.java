package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.app.AppPositionManager;
import com.pokecobble.phone.app.AppRegistry;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.notification.PhoneNotificationRenderer;
import com.pokecobble.phone.notification.PhoneNotificationOverlay;

/**
 * Manages the phone feature.
 */
public class PhoneManager {
    private static PhoneManager instance;

    private PhoneManager() {}

    /**
     * Gets the singleton instance of the PhoneManager.
     */
    public static PhoneManager getInstance() {
        if (instance == null) {
            instance = new PhoneManager();
        }
        return instance;
    }

    /**
     * Initializes the phone feature.
     */
    public static void initialize() {
        Pokecobbleclaim.LOGGER.info("Initializing phone feature");

        // Initialize the app position manager first
        Pokecobbleclaim.LOGGER.info("Initializing app position manager");
        AppPositionManager.getInstance().initialize();

        // Initialize the app registry
        Pokecobbleclaim.LOGGER.info("Initializing app registry");
        AppRegistry.getInstance().initialize();

        // Initialize the phone texture manager
        Pokecobbleclaim.LOGGER.info("Initializing phone texture manager");
        // Just accessing the instance will initialize it
        PhoneTextureManager.getInstance();

        // Initialize and register the phone notification overlay
        Pokecobbleclaim.LOGGER.info("Initializing and registering phone notification overlay");
        PhoneNotificationOverlay.register();

        // Register the phone notification renderer
        Pokecobbleclaim.LOGGER.info("Registering phone notification renderer");
        PhoneNotificationRenderer.register();

        // Register the phone keybinding last
        Pokecobbleclaim.LOGGER.info("Registering phone keybinding");
        PhoneKeybind.register();
    }

    /**
     * Refreshes player data in the phone system.
     */
    public void refreshPlayerData() {
        // Refresh any phone apps that display player data
        // This is called when player data is updated from the server
        Pokecobbleclaim.LOGGER.debug("Refreshing phone player data");

        // Notify all phone apps that player data has been updated
        // Individual apps can implement their own refresh logic
    }
}
