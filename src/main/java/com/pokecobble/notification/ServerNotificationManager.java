package com.pokecobble.notification;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.notification.PhoneNotification;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Server-side notification manager that handles persistent notifications and cross-session history.
 */
public class ServerNotificationManager {
    private static ServerNotificationManager instance;
    
    // Player notifications: player UUID -> list of notifications
    private final Map<UUID, List<ServerNotification>> playerNotifications = new ConcurrentHashMap<>();
    
    // Notification history: player UUID -> list of historical notifications
    private final Map<UUID, List<ServerNotification>> notificationHistory = new ConcurrentHashMap<>();
    
    // Data versioning for synchronization
    private final Map<UUID, Integer> playerNotificationVersions = new ConcurrentHashMap<>();
    
    // Configuration
    private static final int MAX_ACTIVE_NOTIFICATIONS = 20;
    private static final int MAX_NOTIFICATION_HISTORY = 100;
    private static final long NOTIFICATION_CLEANUP_INTERVAL_MS = 60000; // 1 minute
    
    // Cleanup tracking
    private long lastCleanupTime = 0;
    
    private ServerNotificationManager() {}
    
    public static ServerNotificationManager getInstance() {
        if (instance == null) {
            instance = new ServerNotificationManager();
        }
        return instance;
    }
    
    /**
     * Adds a notification for a player.
     */
    public ServerNotification addNotification(UUID playerId, String title, String message, 
                                            ServerNotification.Type type, int durationTicks) {
        return addNotification(playerId, title, message, type, durationTicks, null, null);
    }
    
    /**
     * Adds a notification for a player with additional data.
     */
    public ServerNotification addNotification(UUID playerId, String title, String message, 
                                            ServerNotification.Type type, int durationTicks,
                                            String townName, UUID townId) {
        if (playerId == null || title == null || message == null) {
            return null;
        }
        
        // Create notification
        ServerNotification notification = new ServerNotification(
            UUID.randomUUID(),
            playerId,
            title,
            message,
            type,
            durationTicks,
            System.currentTimeMillis(),
            townName,
            townId
        );
        
        // Add to active notifications
        List<ServerNotification> notifications = playerNotifications.computeIfAbsent(playerId, k -> new ArrayList<>());
        notifications.add(notification);
        
        // Limit active notifications
        if (notifications.size() > MAX_ACTIVE_NOTIFICATIONS) {
            // Move oldest to history
            ServerNotification oldest = notifications.remove(0);
            addToHistory(playerId, oldest);
        }
        
        // Update version
        playerNotificationVersions.put(playerId, playerNotificationVersions.getOrDefault(playerId, 0) + 1);
        
        // Sync to client if player is online
        syncNotificationsToPlayer(playerId);
        
        Pokecobbleclaim.LOGGER.debug("Added notification for player " + playerId + ": " + title);
        return notification;
    }
    
    /**
     * Removes a notification.
     */
    public boolean removeNotification(UUID playerId, UUID notificationId) {
        List<ServerNotification> notifications = playerNotifications.get(playerId);
        if (notifications == null) {
            return false;
        }
        
        boolean removed = notifications.removeIf(n -> n.getId().equals(notificationId));
        if (removed) {
            // Update version
            playerNotificationVersions.put(playerId, playerNotificationVersions.getOrDefault(playerId, 0) + 1);
            
            // Sync to client
            syncNotificationsToPlayer(playerId);
        }
        
        return removed;
    }
    
    /**
     * Marks a notification as read.
     */
    public boolean markNotificationAsRead(UUID playerId, UUID notificationId) {
        List<ServerNotification> notifications = playerNotifications.get(playerId);
        if (notifications == null) {
            return false;
        }
        
        for (ServerNotification notification : notifications) {
            if (notification.getId().equals(notificationId)) {
                notification.markAsRead();
                
                // Update version
                playerNotificationVersions.put(playerId, playerNotificationVersions.getOrDefault(playerId, 0) + 1);
                
                // Sync to client
                syncNotificationsToPlayer(playerId);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Gets all active notifications for a player.
     */
    public List<ServerNotification> getPlayerNotifications(UUID playerId) {
        List<ServerNotification> notifications = playerNotifications.get(playerId);
        return notifications != null ? new ArrayList<>(notifications) : new ArrayList<>();
    }
    
    /**
     * Gets notification history for a player.
     */
    public List<ServerNotification> getPlayerNotificationHistory(UUID playerId) {
        List<ServerNotification> history = notificationHistory.get(playerId);
        return history != null ? new ArrayList<>(history) : new ArrayList<>();
    }
    
    /**
     * Gets unread notifications for a player.
     */
    public List<ServerNotification> getUnreadNotifications(UUID playerId) {
        return getPlayerNotifications(playerId).stream()
                .filter(n -> !n.isRead())
                .toList();
    }
    
    /**
     * Gets the number of unread notifications for a player.
     */
    public int getUnreadNotificationCount(UUID playerId) {
        return getUnreadNotifications(playerId).size();
    }
    
    /**
     * Adds a notification to history.
     */
    private void addToHistory(UUID playerId, ServerNotification notification) {
        List<ServerNotification> history = notificationHistory.computeIfAbsent(playerId, k -> new ArrayList<>());
        history.add(notification);
        
        // Limit history size
        if (history.size() > MAX_NOTIFICATION_HISTORY) {
            history.remove(0);
        }
    }
    
    /**
     * Synchronizes notifications to a player.
     */
    private void syncNotificationsToPlayer(UUID playerId) {
        try {
            // This would be called by the notification synchronizer
            ServerNotificationSynchronizer.syncPlayerNotifications(playerId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing notifications to player " + playerId + ": " + e.getMessage());
        }
    }
    
    /**
     * Cleans up expired notifications.
     */
    public void cleanupExpiredNotifications() {
        long currentTime = System.currentTimeMillis();
        
        // Only cleanup if enough time has passed
        if (currentTime - lastCleanupTime < NOTIFICATION_CLEANUP_INTERVAL_MS) {
            return;
        }
        
        int totalRemoved = 0;
        Set<UUID> affectedPlayers = new HashSet<>();
        
        // Clean up expired notifications
        for (Map.Entry<UUID, List<ServerNotification>> entry : playerNotifications.entrySet()) {
            UUID playerId = entry.getKey();
            List<ServerNotification> notifications = entry.getValue();
            
            int sizeBefore = notifications.size();
            
            // Remove expired notifications and move to history
            Iterator<ServerNotification> iterator = notifications.iterator();
            while (iterator.hasNext()) {
                ServerNotification notification = iterator.next();
                if (notification.isExpired()) {
                    iterator.remove();
                    addToHistory(playerId, notification);
                    affectedPlayers.add(playerId);
                }
            }
            
            int removed = sizeBefore - notifications.size();
            if (removed > 0) {
                totalRemoved += removed;
                // Update version
                playerNotificationVersions.put(playerId, playerNotificationVersions.getOrDefault(playerId, 0) + 1);
            }
        }
        
        // Sync to affected players
        for (UUID playerId : affectedPlayers) {
            syncNotificationsToPlayer(playerId);
        }
        
        lastCleanupTime = currentTime;
        
        if (totalRemoved > 0) {
            Pokecobbleclaim.LOGGER.debug("Cleaned up " + totalRemoved + " expired notifications for " + affectedPlayers.size() + " players");
        }
    }
    
    /**
     * Gets the notification version for a player.
     */
    public int getPlayerNotificationVersion(UUID playerId) {
        return playerNotificationVersions.getOrDefault(playerId, 0);
    }
    
    /**
     * Clears all notifications for a player (called on disconnect).
     */
    public void clearPlayerNotifications(UUID playerId) {
        playerNotifications.remove(playerId);
        playerNotificationVersions.remove(playerId);
        // Keep history for when player reconnects
    }
    
    /**
     * Sends a town-related notification to all players in a town.
     */
    public void sendTownNotification(UUID townId, String townName, String title, String message, 
                                   ServerNotification.Type type, int durationTicks) {
        // This would need access to town members
        // Implementation depends on TownManager integration
        try {
            com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
            if (town != null) {
                for (UUID playerId : town.getPlayerIds()) {
                    addNotification(playerId, title, message, type, durationTicks, townName, townId);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town notification: " + e.getMessage());
        }
    }
    
    /**
     * Sends a notification to all online players.
     */
    public void sendGlobalNotification(MinecraftServer server, String title, String message, 
                                     ServerNotification.Type type, int durationTicks) {
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            addNotification(player.getUuid(), title, message, type, durationTicks);
        }
    }
    
    /**
     * Gets notification statistics.
     */
    public Map<String, Object> getNotificationStats() {
        Map<String, Object> stats = new HashMap<>();
        
        int totalActive = 0;
        int totalHistory = 0;
        int totalUnread = 0;
        
        for (List<ServerNotification> notifications : playerNotifications.values()) {
            totalActive += notifications.size();
            totalUnread += (int) notifications.stream().filter(n -> !n.isRead()).count();
        }
        
        for (List<ServerNotification> history : notificationHistory.values()) {
            totalHistory += history.size();
        }
        
        stats.put("playersWithNotifications", playerNotifications.size());
        stats.put("totalActiveNotifications", totalActive);
        stats.put("totalHistoryNotifications", totalHistory);
        stats.put("totalUnreadNotifications", totalUnread);
        stats.put("lastCleanupTime", lastCleanupTime);
        
        return stats;
    }
    
    /**
     * Converts a ServerNotification to a PhoneNotification for compatibility.
     */
    public static PhoneNotification toPhoneNotification(ServerNotification serverNotification) {
        PhoneNotification.Type phoneType;
        switch (serverNotification.getType()) {
            case TOWN_INVITE -> phoneType = PhoneNotification.Type.TOWN_INVITE;
            case ELECTION -> phoneType = PhoneNotification.Type.ELECTION;
            case MONEY -> phoneType = PhoneNotification.Type.MONEY;
            case SYSTEM -> phoneType = PhoneNotification.Type.SYSTEM;
            default -> phoneType = PhoneNotification.Type.INFO;
        }
        
        PhoneNotification phoneNotification = new PhoneNotification(
            serverNotification.getTitle(),
            serverNotification.getMessage(),
            phoneType,
            serverNotification.getDurationTicks()
        );
        
        phoneNotification.setTownName(serverNotification.getTownName());
        phoneNotification.setTownId(serverNotification.getTownId() != null ? serverNotification.getTownId().toString() : null);
        phoneNotification.setTimestamp(serverNotification.getCreatedTime());
        
        return phoneNotification;
    }
}
