package com.pokecobble.notification;

import java.util.UUID;

/**
 * Represents a server-side notification with persistence and tracking.
 */
public class ServerNotification {
    private final UUID id;
    private final UUID playerId;
    private final String title;
    private final String message;
    private final Type type;
    private final int durationTicks;
    private final long createdTime;
    private final String townName;
    private final UUID townId;
    
    private boolean read;
    private long readTime;
    private int remainingTicks;
    
    public enum Type {
        INFO,
        WARNING,
        ERROR,
        SUCCESS,
        TOWN_INVITE,
        ELECTION,
        MONEY,
        SYSTEM,
        CLAIM,
        PERMISSION
    }
    
    /**
     * Creates a new server notification.
     */
    public ServerNotification(UUID id, UUID playerId, String title, String message, Type type, 
                            int durationTicks, long createdTime, String townName, UUID townId) {
        this.id = id;
        this.playerId = playerId;
        this.title = title;
        this.message = message;
        this.type = type;
        this.durationTicks = durationTicks;
        this.createdTime = createdTime;
        this.townName = townName;
        this.townId = townId;
        this.remainingTicks = durationTicks;
        this.read = false;
        this.readTime = 0;
    }
    
    /**
     * Gets the notification ID.
     */
    public UUID getId() {
        return id;
    }
    
    /**
     * Gets the player ID.
     */
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * Gets the notification title.
     */
    public String getTitle() {
        return title;
    }
    
    /**
     * Gets the notification message.
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Gets the notification type.
     */
    public Type getType() {
        return type;
    }
    
    /**
     * Gets the duration in ticks.
     */
    public int getDurationTicks() {
        return durationTicks;
    }
    
    /**
     * Gets the creation time.
     */
    public long getCreatedTime() {
        return createdTime;
    }
    
    /**
     * Gets the town name (if applicable).
     */
    public String getTownName() {
        return townName;
    }
    
    /**
     * Gets the town ID (if applicable).
     */
    public UUID getTownId() {
        return townId;
    }
    
    /**
     * Checks if the notification has been read.
     */
    public boolean isRead() {
        return read;
    }
    
    /**
     * Gets the time when the notification was read.
     */
    public long getReadTime() {
        return readTime;
    }
    
    /**
     * Gets the remaining ticks.
     */
    public int getRemainingTicks() {
        return remainingTicks;
    }
    
    /**
     * Sets the remaining ticks.
     */
    public void setRemainingTicks(int remainingTicks) {
        this.remainingTicks = remainingTicks;
    }
    
    /**
     * Marks the notification as read.
     */
    public void markAsRead() {
        this.read = true;
        this.readTime = System.currentTimeMillis();
    }
    
    /**
     * Marks the notification as unread.
     */
    public void markAsUnread() {
        this.read = false;
        this.readTime = 0;
    }
    
    /**
     * Checks if the notification is expired.
     */
    public boolean isExpired() {
        return remainingTicks <= 0;
    }
    
    /**
     * Updates the notification (decrements remaining ticks).
     */
    public void tick() {
        if (remainingTicks > 0) {
            remainingTicks--;
        }
    }
    
    /**
     * Gets the age of the notification in milliseconds.
     */
    public long getAge() {
        return System.currentTimeMillis() - createdTime;
    }
    
    /**
     * Gets the age of the notification in seconds.
     */
    public long getAgeSeconds() {
        return getAge() / 1000;
    }
    
    /**
     * Gets the time remaining until expiry in milliseconds (approximate).
     */
    public long getTimeRemaining() {
        if (isExpired()) {
            return 0;
        }
        // Approximate conversion from ticks to milliseconds (20 ticks = 1 second)
        return (remainingTicks * 50);
    }
    
    /**
     * Gets the time remaining until expiry in seconds.
     */
    public long getTimeRemainingSeconds() {
        return getTimeRemaining() / 1000;
    }
    
    /**
     * Checks if this is a town-related notification.
     */
    public boolean isTownRelated() {
        return townId != null || townName != null;
    }
    
    /**
     * Checks if this notification is urgent (warning or error).
     */
    public boolean isUrgent() {
        return type == Type.WARNING || type == Type.ERROR;
    }
    
    /**
     * Checks if this notification is persistent (should not auto-expire).
     */
    public boolean isPersistent() {
        return type == Type.TOWN_INVITE || type == Type.ELECTION;
    }
    
    /**
     * Gets the priority of the notification (higher number = higher priority).
     */
    public int getPriority() {
        return switch (type) {
            case ERROR -> 5;
            case WARNING -> 4;
            case TOWN_INVITE, ELECTION -> 3;
            case MONEY, SYSTEM -> 2;
            case SUCCESS, INFO -> 1;
            default -> 0;
        };
    }
    
    /**
     * Creates a copy of this notification with updated data.
     */
    public ServerNotification copy() {
        ServerNotification copy = new ServerNotification(
            this.id, this.playerId, this.title, this.message, this.type,
            this.durationTicks, this.createdTime, this.townName, this.townId
        );
        copy.read = this.read;
        copy.readTime = this.readTime;
        copy.remainingTicks = this.remainingTicks;
        return copy;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ServerNotification that = (ServerNotification) obj;
        return id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
    
    @Override
    public String toString() {
        return "ServerNotification{" +
                "id=" + id +
                ", playerId=" + playerId +
                ", title='" + title + '\'' +
                ", type=" + type +
                ", read=" + read +
                ", remainingTicks=" + remainingTicks +
                ", age=" + getAgeSeconds() + "s" +
                '}';
    }
}
