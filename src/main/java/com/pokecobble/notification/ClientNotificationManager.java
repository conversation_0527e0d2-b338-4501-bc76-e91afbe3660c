package com.pokecobble.notification;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side notification manager.
 * Manages cached notification data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientNotificationManager {
    private static ClientNotificationManager instance;
    
    // Cached notification data
    private final Map<UUID, ServerNotification> notifications = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Version tracking
    private int notificationVersion = -1;
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 60000; // 1 minute
    
    private ClientNotificationManager() {}
    
    public static ClientNotificationManager getInstance() {
        if (instance == null) {
            instance = new ClientNotificationManager();
        }
        return instance;
    }
    
    /**
     * Updates a notification in the cache.
     */
    public void updateNotification(ServerNotification notification, int version) {
        if (notification == null) {
            return;
        }
        
        // Only update if version is newer
        if (version > notificationVersion) {
            notifications.put(notification.getId(), notification);
            lastUpdateTimes.put(notification.getId(), System.currentTimeMillis());
            
            notificationVersion = version;
            
            Pokecobbleclaim.LOGGER.debug("Updated notification cache for " + notification.getId() + " (version " + version + ")");
        }
    }
    
    /**
     * Gets a notification from the cache.
     */
    public ServerNotification getNotification(UUID notificationId) {
        if (notificationId == null) {
            return null;
        }
        
        // Check if cache is expired
        Long lastUpdate = lastUpdateTimes.get(notificationId);
        if (lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS) {
            // Cache expired, remove from cache
            invalidateNotification(notificationId);
            return null;
        }
        
        return notifications.get(notificationId);
    }
    
    /**
     * Gets all cached notifications.
     */
    public List<ServerNotification> getAllNotifications() {
        List<ServerNotification> result = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        
        for (Map.Entry<UUID, ServerNotification> entry : notifications.entrySet()) {
            UUID notificationId = entry.getKey();
            ServerNotification notification = entry.getValue();
            
            // Check if cache is expired
            Long lastUpdate = lastUpdateTimes.get(notificationId);
            if (lastUpdate != null && currentTime - lastUpdate > CACHE_TIMEOUT_MS) {
                // Cache expired, skip this notification
                continue;
            }
            
            result.add(notification);
        }
        
        // Sort by creation time (newest first)
        result.sort((a, b) -> Long.compare(b.getCreatedTime(), a.getCreatedTime()));
        
        return result;
    }
    
    /**
     * Gets all unread notifications.
     */
    public List<ServerNotification> getUnreadNotifications() {
        return getAllNotifications().stream()
                .filter(n -> !n.isRead())
                .toList();
    }
    
    /**
     * Gets the number of unread notifications.
     */
    public int getUnreadNotificationCount() {
        return getUnreadNotifications().size();
    }
    
    /**
     * Checks if there are any unread notifications.
     */
    public boolean hasUnreadNotifications() {
        return getUnreadNotificationCount() > 0;
    }
    
    /**
     * Gets notifications by type.
     */
    public List<ServerNotification> getNotificationsByType(ServerNotification.Type type) {
        return getAllNotifications().stream()
                .filter(n -> n.getType() == type)
                .toList();
    }
    
    /**
     * Gets town-related notifications.
     */
    public List<ServerNotification> getTownNotifications() {
        return getAllNotifications().stream()
                .filter(ServerNotification::isTownRelated)
                .toList();
    }
    
    /**
     * Gets urgent notifications (warnings and errors).
     */
    public List<ServerNotification> getUrgentNotifications() {
        return getAllNotifications().stream()
                .filter(ServerNotification::isUrgent)
                .toList();
    }
    
    /**
     * Gets the most recent notification.
     */
    public ServerNotification getMostRecentNotification() {
        return getAllNotifications().stream()
                .max(Comparator.comparing(ServerNotification::getCreatedTime))
                .orElse(null);
    }
    
    /**
     * Gets notifications that are expiring soon.
     */
    public List<ServerNotification> getExpiringSoonNotifications() {
        long fiveMinutes = 300000; // 5 minutes in milliseconds
        
        return getAllNotifications().stream()
                .filter(n -> !n.isExpired() && n.getTimeRemaining() < fiveMinutes)
                .toList();
    }
    
    /**
     * Marks a notification as read.
     */
    public void markAsRead(UUID notificationId) {
        ServerNotification notification = getNotification(notificationId);
        if (notification != null && !notification.isRead()) {
            // Mark locally
            notification.markAsRead();
            
            // Send to server
            ServerNotificationSynchronizer.sendMarkReadRequest(notificationId);
            
            Pokecobbleclaim.LOGGER.debug("Marked notification " + notificationId + " as read");
        }
    }
    
    /**
     * Removes a notification.
     */
    public void removeNotification(UUID notificationId) {
        ServerNotification notification = getNotification(notificationId);
        if (notification != null) {
            // Remove locally
            invalidateNotification(notificationId);
            
            // Send to server
            ServerNotificationSynchronizer.sendRemoveRequest(notificationId);
            
            Pokecobbleclaim.LOGGER.debug("Removed notification " + notificationId);
        }
    }
    
    /**
     * Marks all notifications as read.
     */
    public void markAllAsRead() {
        for (ServerNotification notification : getUnreadNotifications()) {
            markAsRead(notification.getId());
        }
    }
    
    /**
     * Removes all read notifications.
     */
    public void removeAllRead() {
        for (ServerNotification notification : getAllNotifications()) {
            if (notification.isRead()) {
                removeNotification(notification.getId());
            }
        }
    }
    
    /**
     * Invalidates a notification from the cache.
     */
    public void invalidateNotification(UUID notificationId) {
        notifications.remove(notificationId);
        lastUpdateTimes.remove(notificationId);
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        notifications.clear();
        lastUpdateTimes.clear();
        notificationVersion = -1;
        
        Pokecobbleclaim.LOGGER.debug("Cleared notification cache");
    }
    
    /**
     * Gets the current notification version.
     */
    public int getNotificationVersion() {
        return notificationVersion;
    }
    
    /**
     * Checks if notifications need to be refreshed from the server.
     */
    public boolean needsRefresh() {
        // Check if we have any cached data
        if (notifications.isEmpty()) {
            return true;
        }
        
        // Check if any cached data is expired
        long currentTime = System.currentTimeMillis();
        for (Long lastUpdate : lastUpdateTimes.values()) {
            if (currentTime - lastUpdate > CACHE_TIMEOUT_MS) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Requests notification sync from server.
     */
    public void requestSync() {
        ServerNotificationSynchronizer.requestNotificationSync();
    }
    
    /**
     * Requests notification history from server.
     */
    public void requestHistory() {
        ServerNotificationSynchronizer.requestNotificationHistory();
    }
    
    /**
     * Gets cache statistics.
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedNotifications", notifications.size());
        stats.put("unreadNotifications", getUnreadNotificationCount());
        stats.put("notificationVersion", notificationVersion);
        stats.put("urgentNotifications", getUrgentNotifications().size());
        stats.put("townNotifications", getTownNotifications().size());
        stats.put("expiringSoon", getExpiringSoonNotifications().size());
        
        // Count notifications by type
        Map<String, Integer> typeCounts = new HashMap<>();
        for (ServerNotification notification : getAllNotifications()) {
            String type = notification.getType().name();
            typeCounts.put(type, typeCounts.getOrDefault(type, 0) + 1);
        }
        stats.put("typeCounts", typeCounts);
        
        // Count notifications by read status
        long readCount = getAllNotifications().stream().filter(ServerNotification::isRead).count();
        stats.put("readNotifications", readCount);
        stats.put("unreadNotifications", getAllNotifications().size() - readCount);
        
        return stats;
    }
    
    /**
     * Gets a summary of notification activity.
     */
    public String getNotificationSummary() {
        int total = getAllNotifications().size();
        int unread = getUnreadNotificationCount();
        int urgent = getUrgentNotifications().size();
        
        if (total == 0) {
            return "No notifications";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append(total).append(" notification").append(total != 1 ? "s" : "");
        
        if (unread > 0) {
            summary.append(" (").append(unread).append(" unread");
            if (urgent > 0) {
                summary.append(", ").append(urgent).append(" urgent");
            }
            summary.append(")");
        }
        
        return summary.toString();
    }
}
