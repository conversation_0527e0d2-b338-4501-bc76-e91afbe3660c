package com.pokecobble;

import com.pokecobble.command.PerformanceCommand;
import com.pokecobble.config.PerformanceConfig;
import com.pokecobble.debug.network.PacketMonitorKeybind;
import com.pokecobble.phone.PhoneManager;
import com.pokecobble.tools.gui.ShapeVisualizerTool;
import com.pokecobble.town.client.ChunkTracker;
import com.pokecobble.town.client.KeyBindings;
import com.pokecobble.town.client.WorldRenderHandler;
import com.pokecobble.town.client.ChunkRenderTest;
import com.pokecobble.town.command.ErrorLogCommand;
import com.pokecobble.town.command.TownCommand;
import com.pokecobble.town.keybind.TownKeybind;
import com.pokecobble.town.sound.SoundRegistry;
import com.pokecobble.util.PerformanceMonitor;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;

/**
 * Client-side initialization for the PokeCobbleClaim mod.
 */
@Environment(EnvType.CLIENT)
public class PokecobbleclaimClient implements ClientModInitializer {

    @Override
    public void onInitializeClient() {
        Pokecobbleclaim.LOGGER.info("Initializing PokeCobbleClaim client");

        // Register client-side commands
        com.pokecobble.town.command.TownClientCommand.registerClientCommands();
        PerformanceCommand.register();

        // Register error log command
        net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
            dispatcher.register(
                net.fabricmc.fabric.api.client.command.v2.ClientCommandManager.literal("errorlog")
                    .executes(context -> {
                        MinecraftClient client = MinecraftClient.getInstance();
                        client.execute(() -> {
                            client.setScreen(new com.pokecobble.town.gui.ErrorLogScreen(client.currentScreen));
                        });
                        return 1;
                    })
            );
        });

        // Register client commands
        net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
            com.pokecobble.town.client.ClientSyncTestCommand.register(dispatcher);
        });

        // Register keybinding
        TownKeybind.register();

        // Register claim system components
        KeyBindings.register();
        WorldRenderHandler.register(); // Re-enabled for claim tool UI
        ChunkRenderTest.register(); // Register chunk render test

        // Initialize client-side data managers
        initializeClientDataManagers();

        // Register sounds - only register on client side
        SoundRegistry.register();
        Pokecobbleclaim.LOGGER.info("Registered sounds on client side");

        // Register error log network handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            com.pokecobble.town.network.ErrorLogNetworkHandler.OPEN_ERROR_LOG,
            (client, handler, buf, responseSender) -> {
                // Execute on the main thread
                client.execute(() -> {
                    // Open the error log screen
                    client.setScreen(new com.pokecobble.town.gui.ErrorLogScreen(client.currentScreen));
                });
            }
        );

        // Initialize phone feature
        PhoneManager.initialize();
        Pokecobbleclaim.LOGGER.info("Initialized phone feature");

        // Register shape visualizer tool
        ShapeVisualizerTool.register();
        Pokecobbleclaim.LOGGER.info("Registered shape visualizer tool");

        // Register debug components (only if enabled in config)
        if (PerformanceConfig.isPacketMonitoringEnabled()) {
            PacketMonitorKeybind.register();
        }

        // Register performance monitoring tick event
        if (PerformanceConfig.isPerformanceMonitoringEnabled()) {
            ClientTickEvents.END_CLIENT_TICK.register(client -> {
                // Log performance data every 1000 ticks (about 50 seconds)
                if (client.player != null && client.world != null && client.world.getTime() % 1000 == 0) {
                    PerformanceMonitor.logPerformanceData();
                    PerformanceMonitor.reset();
                }
            });
        }



        Pokecobbleclaim.LOGGER.info("PokeCobbleClaim client initialized successfully");
    }

    /**
     * Initializes client-side data managers for proper server-client synchronization.
     */
    private static void initializeClientDataManagers() {
        // Initialize all client-side data managers
        com.pokecobble.town.client.ClientTownManager.getInstance();
        com.pokecobble.town.client.ClientPlayerManager.getInstance();
        com.pokecobble.town.client.ClientChunkManager.getInstance();
        com.pokecobble.town.client.ClientElectionManager.getInstance();
        com.pokecobble.town.client.ClientMoneyManager.getInstance();

        // Initialize client sync manager
        com.pokecobble.town.client.ClientSyncManager.getInstance();

        // Initialize client invitation manager
        com.pokecobble.town.client.ClientInvitationManager.getInstance();

        // Initialize client notification manager
        com.pokecobble.notification.ClientNotificationManager.getInstance();



        // Initialize image cache manager
        com.pokecobble.town.image.TownImageCacheManager.getInstance();

        // Initialize claim tool data synchronizer
        com.pokecobble.town.client.ClaimToolDataSynchronizer.getInstance();

        // Initialize UI refresh systems
        com.pokecobble.ui.UIDataRefreshManager.getInstance();
        com.pokecobble.ui.ScreenLifecycleTracker.getInstance();

        // Initialize user preferences manager
        com.pokecobble.config.UserPreferencesManager.getInstance();

        // Initialize town settings manager
        com.pokecobble.town.config.TownSettingsManager.getInstance();

        Pokecobbleclaim.LOGGER.info("Initialized client-side data managers for synchronization");
    }


}
