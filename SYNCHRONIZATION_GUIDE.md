# PokéCobbleClaim Synchronization System

This document describes the comprehensive server-client data synchronization system implemented for the PokéCobbleClaim mod.

## Overview

The synchronization system ensures that all data between the server and client stays consistent and up-to-date. It includes proper versioning, error handling, retry mechanisms, and event-driven updates.

## Architecture

### Server-Side Components

1. **SynchronizationManager** (`com.pokecobble.town.network.SynchronizationManager`)
   - Central coordinator for all synchronization operations
   - Handles retry mechanisms and error recovery
   - Tracks sync status for all players
   - Provides periodic sync validation

2. **Data Synchronizers**
   - `TownDataSynchronizer` - Handles town data sync
   - `PlayerDataSynchronizer` - Handles player data sync
   - `ChunkDataSynchronizer` - Handles chunk/claim data sync
   - `ElectionNetworkHandler` - Handles election data sync
   - `MoneyNetworkHandler` - Handles money/balance data sync

### Client-Side Components

1. **Client Data Managers**
   - `ClientTownManager` - Caches town data with versioning
   - `ClientPlayerManager` - Caches player data with versioning
   - `ClientChunkManager` - Caches chunk/claim data with versioning
   - `ClientElectionManager` - Caches election data with versioning
   - `ClientMoneyManager` - Caches money data with versioning

2. **ClientSyncManager** (`com.pokecobble.town.client.ClientSyncManager`)
   - Event-driven synchronization coordinator
   - Manages sync events and notifications
   - Updates UI components when data changes

## Features

### 1. Version Tracking
- Each data type has version numbers to prevent stale updates
- Only newer versions are accepted and cached
- Prevents race conditions and data corruption

### 2. Error Handling and Retry
- Automatic retry with exponential backoff for failed syncs
- Maximum retry attempts to prevent infinite loops
- Comprehensive error logging and reporting

### 3. Event-Driven Updates
- Real-time UI updates when data changes
- Automatic refresh of dependent components
- Efficient update propagation

### 4. Cache Management
- Intelligent caching with timeout-based invalidation
- Memory-efficient storage with size limits
- Automatic cleanup of stale data

### 5. Performance Optimization
- Batch updates for multiple chunks
- Rate limiting to prevent network spam
- Efficient delta synchronization

## Data Types Synchronized

### Town Data
- Town information (name, description, settings)
- Player membership and ranks
- Claim tags and permissions
- Claim count and limits

### Player Data
- Player permissions within towns
- Player ranks and roles
- Town membership status
- Authentication tokens

### Chunk/Claim Data
- Chunk ownership information
- Claim tags and their properties
- Permission settings per chunk
- Batch updates for efficiency

### Election Data
- Active elections in towns
- Candidate information and vote counts
- Player voting status
- Election results and winners

### Money Data
- Player balance information
- Transaction history
- Real-time balance updates
- Transfer confirmations

## Usage

### Testing Synchronization

#### Server Commands
```
/synctest status    - Show sync status for current player
/synctest force     - Force full resynchronization
/synctest test      - Run comprehensive sync tests
/synctest stats     - Show synchronization statistics
/synctest clear     - Clear cache (client-side)
```

#### Client Commands
```
/clientsync status  - Show client-side sync status
/clientsync stats   - Show client cache statistics
/clientsync clear   - Clear client-side cache
/clientsync test    - Run client-side sync tests
```

### Programming Interface

#### Server-Side Synchronization
```java
// Force sync for a specific player
SynchronizationManager.getInstance().syncAllPlayerData(server, player);

// Sync specific data type with retry
SynchronizationManager.getInstance().syncWithRetry(server, player, 
    SynchronizationManager.SYNC_TOWN_DATA, () -> {
        TownDataSynchronizer.syncPlayerTownData(server, playerId);
    });
```

#### Client-Side Event Handling
```java
// Listen for town updates
ClientSyncManager.getInstance().addEventListener(
    ClientSyncManager.EVENT_TOWN_UPDATED, 
    (data) -> {
        // Handle town update
        Map<String, Object> updateData = (Map<String, Object>) data;
        UUID townId = (UUID) updateData.get("townId");
        // Update UI components
    }
);
```

#### Client-Side Data Access
```java
// Get cached town data
Town town = ClientTownManager.getInstance().getTown(townId);

// Get cached player data
TownPlayer player = ClientPlayerManager.getInstance().getPlayer(playerId);

// Get cached chunk data
UUID chunkOwner = ClientChunkManager.getInstance().getChunkOwner(chunkPos);
ClaimTag chunkTag = ClientChunkManager.getInstance().getChunkTag(chunkPos);
```

## Configuration

### Synchronization Settings
- `MAX_RETRY_ATTEMPTS` - Maximum retry attempts (default: 3)
- `RETRY_DELAY_MS` - Base retry delay (default: 1000ms)
- `SYNC_TIMEOUT_MS` - Sync timeout (default: 30 seconds)
- `PERIODIC_SYNC_INTERVAL_MS` - Periodic sync check interval (default: 1 minute)

### Cache Settings
- `CACHE_TIMEOUT_MS` - Cache expiration time (default: 30 seconds)
- `MAX_CACHED_CHUNKS` - Maximum cached chunks (default: 10,000)
- `MAX_TRANSACTION_HISTORY` - Maximum transaction history (default: 100)

## Troubleshooting

### Common Issues

1. **Sync Failures**
   - Check network connectivity
   - Verify packet size limits
   - Check server logs for errors
   - Use `/synctest force` to retry

2. **Stale Data**
   - Clear client cache with `/clientsync clear`
   - Check version numbers in logs
   - Verify sync events are firing

3. **Performance Issues**
   - Monitor sync statistics with `/synctest stats`
   - Check for excessive retry attempts
   - Verify rate limiting is working

### Debug Information

Enable debug logging by setting log level to DEBUG in your logging configuration:
```
com.pokecobble.town.network=DEBUG
com.pokecobble.town.client=DEBUG
```

### Monitoring

Use the statistics commands to monitor synchronization health:
- Server stats show retry attempts and sync status
- Client stats show cache usage and event activity
- Both provide insights into system performance

## Best Practices

1. **Always use the client managers** instead of accessing server data directly on the client
2. **Handle sync events** to update UI components when data changes
3. **Check cache validity** before using cached data in critical operations
4. **Use batch operations** when updating multiple chunks or players
5. **Monitor sync statistics** in production to identify issues early

## Future Improvements

- Implement delta synchronization for large data sets
- Add compression for large packets
- Implement priority-based synchronization
- Add metrics collection for performance monitoring
- Implement automatic sync health checks
